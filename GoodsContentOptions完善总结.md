# GoodsContentOptions完善总结

## 概述
根据`client/route/batch-operate/modify/fields.tsx`文件中的所有字段信息，完善了`client/route/batch-operate/list/constant.ts`文件中的`GoodsContentOptions`，将所有可批量操作的字段都添加到了选项列表中。

## 主要工作内容

### 1. 导入字段常量
从`fields.tsx`文件中导入了所有字段常量，包括：

#### 商品库商品字段（25个）
- `STORAGE_GOODS_STATUS` - 是否可售
- `STORAGE_GOODS_CATEGORY` - 商品分类
- `STORAGE_GOODS_NAME` - 商品名称
- `STORAGE_PHOTO_URL` - 商品图片
- `GOODS_SUPPLIER` - 首选供应商
- `GOODS_BRAND` - 商品品牌
- `ONLINE_GOODS_ORG` - 店内组织
- `GOODS_LIFE_CYCLE` - 生命周期
- `DELIVERY_TYPE` - 物流模式
- `STORAGE_CATEGORY` - 商品类目
- `STORAGE_CATEGORY_DESC` - 商品类目描述
- `STORAGE_GOODS_NO` - 商品条码
- `SPU_SELL_CHANNEL` - 可售渠道
- `SPU_PRODUCTION_TIME` - 加工时长
- `GOODS_ATTR` - 商品属性
- `STORAGE_GOODS_CODE` - 商品编码
- `STORAGE_SKU_PATTERN` - 规格型号
- `STORAGE_INPUT_TAX_RATE` - 进项税率
- `STORAGE_OUTPUT_TAX_RATE` - 销项税率
- `STORAGE_SHELF_LIFE_SETTING` - 保质期管理
- `STORAGE_SUB_TITLE` - 分享描述
- `SELL_POINT` - 商品卖点

#### 门店商品字段（10个）
- `OFFLINE_GOODS_STATUS` - 门店商品状态
- `OFFLINE_GOODS_NAME` - 门店商品名称
- `OFFLINE_MEMBER_DISCOUNT` - 门店会员折扣
- `OFFLINE_GOODS_GROUP` - 门店商品分组
- `OFFLINE_SCHEDULE_DISPLAY` - 门店定时上下架
- `OFFLINE_BUY_LIMIT` - 门店每人限购数
- `OFFLINE_START_SALE_NUM` - 门店起售数量
- `OFFLINE_CRUCIAL_LABEL` - 门店商品关键标签
- `OFFLINE_GENERAL_LABEL` - 门店商品普通标签
- `OFFLINE_PURCHASE_RIGHT_REQUEST` - 门店身份限购

#### 网店商品字段（27个）
- `ONLINE_GOODS_STATUS` - 网店商品状态
- `ONLINE_GOODS_GROUP` - 网店商品分组
- `ONLINE_GOODS_TEMPLATE` - 商品页模板
- `ONLINE_GOODS_NAME` - 网店商品名称
- `ONLINE_MEMBER_DISCOUNT` - 网店会员折扣
- `ONLINE_SALE_TIME` - 开售时间
- `SCHEDULE_DISPLAY_OFF` - 定时下架
- `ONLINE_SCHEDULE_DISPLAY` - 网店定时上下架
- `ONLINE_BUY_LIMIT` - 网店每人限购数
- `PURCHASE_RIGHT_REQUEST` - 网店身份限购
- `ONLINE_DELIVERY_TEMPLATE` - 运费模板
- `ONLINE_LOGISTICS_TIMELINESS_TEMPLATE` - 物流时效模板
- `ONLINE_ORIGIN` - 划线价
- `ONLINE_CATEGORY` - 网店商品类目
- `ONLINE_CATEGORY_DESC` - 网店商品类目描述
- `ONLINE_AFTER_SALE` - 售后服务
- `ONLINE_GOODS_NO` - 网店商品编码
- `ONLINE_DISTRIBUTION` - 配送方式
- `ONLINE_BARCODE` - 网店商品条码
- `ONLINE_START_SALE_NUM` - 网店起售数量
- `ONLINE_SALE_TYPE` - 售卖方式
- `ONLINE_SHOW_STOCK` - 是否展示库存
- `ONLINE_PREPARE_TIME` - 备货时间
- `ONLINE_GOODS_INDEX` - 序号
- `ONLINE_PRODUCTION_TIME` - 网店加工时长
- `ONLINE_CLASSIFICATION` - 商品分类
- `ONLINE_BRAND` - 网店商品品牌
- `ONLINE_HEAVY_CONTINUED` - 续重收费
- `ONLINE_STOCK_DEDUCTION_MODE` - 库存扣减方式
- `ONLINE_CRUCIAL_LABEL` - 网店商品关键标签
- `ONLINE_GENERAL_LABEL` - 网店商品普通标签
- `ONLINE_MESSAGES` - 商品留言

#### 外卖渠道商品字段（10个）

**美团外卖商品字段（3个）**
- `MEITUAN_GOODS_STATUS` - 美团外卖商品状态
- `MEITUAN_GOODS_GROUP` - 美团外卖商品分组
- `MEITUAN_PACKING_CHARGES` - 美团外卖打包费

**饿了么外卖商品字段（3个）**
- `ELEME_GOODS_STATUS` - 饿了么外卖商品状态
- `ELEME_GOODS_GROUP` - 饿了么外卖商品分组
- `ELEME_PACKING_CHARGES` - 饿了么外卖打包费

**美团闪购商品字段（2个）**
- `MEITUAN_SHANGOU_GOODS_STATUS` - 美团闪购商品状态
- `MEITUAN_SHANGOU_GOODS_GROUP` - 美团闪购商品分组

**京东外卖商品字段（3个）**
- `JD_WM_GOODS_STATUS` - 京东外卖商品状态
- `JD_WM_GOODS_GROUP` - 京东外卖商品分组
- `JD_WM_PACKING_CHARGES` - 京东外卖打包费

### 2. 完善GoodsContentOptions
将所有字段都添加到`GoodsContentOptions`数组中，每个选项包含：
- `text`: 用户友好的显示文本
- `value`: 对应的字段常量数组

### 3. 字段分类组织
按照商品类型对字段进行了清晰的分类：
1. 商品库商品字段
2. 门店商品字段  
3. 网店商品字段
4. 外卖渠道商品字段（美团外卖、饿了么外卖、美团闪购、京东外卖）

### 4. 优化处理
- 移除了内部使用的渠道字段（`OFFLINE_CHANNEL`、`ONLINE_CHANNEL`）
- 保持了原有的"全部"选项和现有的字段组合
- 为每个字段提供了清晰的中文描述

## 总计字段数量
- **商品库商品字段**: 22个
- **门店商品字段**: 10个  
- **网店商品字段**: 27个
- **外卖渠道商品字段**: 10个
- **总计**: 69个可批量操作的字段

## 使用效果
现在用户在批量操作列表页面可以：
1. 选择"全部"查看所有批量操作记录
2. 根据具体修改的字段类型进行筛选
3. 快速定位到特定类型的批量操作记录
4. 支持所有渠道（商品库、门店、网店、外卖渠道）的字段筛选

这样的完善使得批量操作的筛选功能更加全面和实用，用户可以精确地找到他们需要的批量操作记录。
