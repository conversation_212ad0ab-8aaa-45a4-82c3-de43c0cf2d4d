import { useEffect, useReducer, useRef } from 'react';
import { usePolling } from '@youzan/react-hooks';
import { identity, noop, isEqual, omit } from 'lodash';
import { batchSlice, batchPush, diffList, arr2obj } from '@youzan/retail-utils';
import { useCurrent } from 'common/hooks';
import {
  pollConfig,
  taskStatusMap,
  operateTypeMap,
  actionMap,
  defaultStatus,
  batchFetchError
} from './helper';

interface IState {
  loading: boolean;
  startPolling: boolean;
  taskId: string | null;
  originDatasets: unknown[];
  tempBaseInfo: Record<string, any>;
}

interface IAction {
  type: string;
  datasets?: unknown[];
  taskId?: string | null;
  baseInfo?: Record<string, any>;
}

interface ISubmitParams {
  params: Record<string, any>;
  exceptionProcess: boolean;
  taskId: string;
}
interface ITransformedParams<T> {
  taskId: string;
  started: boolean;
  exceptionProcess: boolean;
  forCreate: boolean;
  batchNo: number;
  items: Array<T>;
  finished: boolean;
}
interface IOptions<T = any> {
  isEdit?: boolean;
  createTaskApi: (params: Record<string, any>) => Promise<string>;
  datasets: unknown[];
  createTaskCache?: boolean;
  handleParams?: (params: ITransformedParams<T>) => unknown;
  // 成功回调
  onSuccess?: () => unknown;
  // 存在异常数据
  // @NEED generic
  onGoodsError?: (errorItems: any) => unknown;
  // 接口失败处理
  onFail?: (err: IError) => unknown;
  // 超过最大轮训次数
  onTaskTimeout?: () => unknown;
  //
  queryStatusApi?: QueryStatusApi;
  diffListOptions?: {
    compareKeys?: string[];
    uniqKey?: string;
    errorItemUniqKey?: string;
  };
  queryErrorsApi?: QueryErrorsApi;
  chunk?: number;
  submitItemsApi?: SubmitItemsApi;
  onSubmitSuccess?: () => void;
}

interface IPageInfo {
  pageSize: number;
  pageNo: number;
  taskId: string;
}

interface QueryStatusApi {
  ({ taskId }: { taskId: string }): Promise<{ status: string }>;
}

interface QueryErrorsApi {
  (
    params: {
      /** 任务 ID */
      taskId?: string;
      /** 明细类型 */
      itemType: string;
    } & IPageInfo
  ): Promise<{ errorData: { errorItems?: any[]; totalCount: number } } | boolean>;
}

interface SubmitItemsApi {
  (value?: any): Promise<any>;
}

export function reducer(state: IState, action: IAction) {
  switch (action.type) {
    // 批量拉取
    case actionMap.fetched:
      return {
        ...state,
        originDatasets: action.datasets,
        loading: false
      };
    // 提交
    case actionMap.submit:
      return {
        ...state,
        loading: true
      };
    // 批量提交失败
    case actionMap.error:
      return {
        ...state,
        loading: false,
        taskId: null
      };
    // 提交结果轮询
    case actionMap.startPolling:
      return {
        ...state,
        startPolling: true,
        taskId: action.taskId,
        tempBaseInfo: action.baseInfo
      };
    // 存在异常停止轮询
    case actionMap.hasError:
      return {
        ...state,
        startPolling: false
      };
    case actionMap.stop:
      return {
        ...state,
        startPolling: false,
        loading: false
      };
    case actionMap.reset:
      return defaultStatus;
    default:
      return state;
  }
}

function useBatch(options: IOptions) {
  const {
    // 是否为编辑
    isEdit = false,
    // 创建任务api
    createTaskApi,
    // 提交api
    submitItemsApi,
    // 状态查询api
    queryStatusApi,
    // 异常查询api
    queryErrorsApi,
    // diffList option
    diffListOptions,
    // handleParams
    handleParams = identity,
    // datasets
    datasets = [],
    // 成功回调
    onSuccess = noop,
    // 存在异常数据
    onGoodsError = noop,
    // 接口失败处理
    onFail = noop,
    // 超过最大轮训次数
    onTaskTimeout = noop,
    // chunk
    chunk,
    // 是否每次提交任务前都创建单据
    createTaskCache = true,
    onSubmitSuccess
  } = options || ({} as IOptions);

  const datasetRef = useCurrent(datasets);

  const { compareKeys, uniqKey = 'skuId', errorItemUniqKey } = diffListOptions;

  const [status, setStatus] = useReducer<(state: IState, action: IAction) => IState>(reducer, {
    ...defaultStatus,
    loading: isEdit
  });
  // 上一批提交的明细缓存
  const tempDatasets = useRef([]);

  // 查询异常明细
  const queryErrorItems = () => {
    // 批量拉取错误信息
    batchFetchError(queryErrorsApi, status.taskId)
      .then(errorItems => {
        const goodsMap = arr2obj(datasetRef.current, errorItemUniqKey || uniqKey);
        // 存在被删除商品，不返回商品名称
        const data = errorItems.map((item: Record<string, any>) => {
          return {
            ...item,
            ...omit(goodsMap[item[errorItemUniqKey || uniqKey]] as Record<string, unknown>, [
              'errorMsg'
            ])
          };
        });
        onGoodsError(data);
      })
      .catch(err => onFail(err))
      .finally(() => {
        setStatus({ type: actionMap.stop });
      });
  };

  const handlePollRes = (res: { status: string }, count: number) => {
    if (res.status === taskStatusMap.HANDLED) {
      onSuccess(res);
      setStatus({ type: actionMap.stop });
    } else if (res.status === taskStatusMap.ERROR) {
      // 存在异常数据
      queryErrorItems();
      setStatus({ type: actionMap.hasError });
    } else if (count > pollConfig.maxRequestNum) {
      // 超过轮询最大次数，notify数据处理中，请稍后，跳转列表页
      onTaskTimeout();
      setStatus({ type: actionMap.stop });
    }
  };

  usePolling(
    count => {
      return queryStatusApi({ taskId: status.taskId })
        .then(res => handlePollRes(res, count))
        .catch(() => {
          if (count > pollConfig.maxRequestNum) {
            onTaskTimeout();
            setStatus({ type: actionMap.stop });
          }
        });
    },
    pollConfig.interval,
    status.startPolling
  );

  const { startPush, clearPush } = batchPush();

  // 批次号记录
  const currentBatch = useRef(0);

  // 批量提交
  const onSubmit = ({ params, exceptionProcess, taskId }: ISubmitParams) => {
    console.log('params', params);
    
    // 新增 && 再来一单，且未出现异常商品信息的不用diff
    const data =
      !isEdit && !exceptionProcess
        ? datasetRef.current
        : diffList(tempDatasets.current, datasetRef.current, {
            uniqKey,
            compareKeys,
            operateType: operateTypeMap
          });

    const tasks = batchSlice({
      items: data,
      fetch: submitItemsApi,
      startBatchNo: currentBatch.current + 1,
      chunk,
      handleParams: ({ items, finish, batchNo }) => {
        currentBatch.current = batchNo;

        const ssss = handleParams({
          taskId,
          items: items.map(item => {
            item.taskId = taskId;
            item.end = finish;
            item.operateType = item.operateType || operateTypeMap.add;
            return item;
          }),
          batchNo,
          started: batchNo === 1,
          finished: finish,
          exceptionProcess,
          forCreate: !isEdit
        })
        console.log(`sssss`, ssss);
        

        return ssss;
      }
    });

    // return

    // feature 唯一码
    // 涉及唯一码的不同处理，目前通过handleParams让业务方自行拼接
    // 之后考虑加入唯一码基本处理
    startPush(tasks)
      .then(() => {
        if (typeof onSubmitSuccess === 'function') {
          onSubmitSuccess();
        }
        tempDatasets.current = datasetRef.current;
        setStatus({
          type: actionMap.startPolling,
          taskId,
          baseInfo: omit(params, 'taskId')
        });
      })
      .catch(err => {
        setStatus({ type: actionMap.error });
        onFail(err);
      });
  };

  const batchSubmit = (params: Record<string, any>) => {
    setStatus({ type: actionMap.submit });
    // 基本信息没修改不重新创建task
    if (status.taskId && isEqual(params, status.tempBaseInfo) && createTaskCache) {
      onSubmit({
        params,
        taskId: status.taskId,
        exceptionProcess: true
      });
      return;
    }

    // 修改了基本明细从头走一遍
    currentBatch.current = 0;
    tempDatasets.current = status.originDatasets;

    createTaskApi(params)
      .then((res: string) => {
        onSubmit({
          params,
          taskId: res,
          exceptionProcess: false
        });
      })
      .catch((err: unknown) => {
        setStatus({ type: actionMap.stop });
        onFail(err);
      });
  };

  useEffect(() => {
    return () => {
      clearPush();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    status,
    setStatus,
    batchSubmit
  };
}

useBatch.actionMap = actionMap;
export default useBatch;
