import { range } from 'lodash';
import { planQueryOneUrl, PlanRetryUrl, WItemRetryUrl } from './api';

export interface ITaskType {
  value: number[];
  text: string;
  retryUrl?: string;
  queryUrl?: string;
}

export const PLAN_TASK_TYPE = 111;

/** 导出任务类型 Map */
export const taskTypeMap: Record<string, ITaskType> = {
  storage: {
    value: [511, 512],
    text: '商品库商品'
  },
  library: {
    value: [577],
    text: '商品库'
  },
  store: {
    value: [502],
    text: '门店商品'
  },
  liteStore: {
    value: [578],
    text: '门店商品'
  },
  shop: {
    value: [501],
    text: '网店商品'
  },
  shopLite: {
    value: [575, 576],
    text: '网店商品'
  },
  salepriceManage: {
    value: [505],
    text: '零售价格'
  },
  combine: {
    value: [514],
    text: '组合商品'
  },
  batch: {
    // 定时下架新增了range(591, 594) @叶圣涛
    value: [
      ...[542],
      ...range(544, 561),
      ...range(573, 575),
      ...range(591, 594),
      // 下文新增 579, 601, 602, 603, 604 @竟陵
      // 多渠道新增606
      ...[579, 601, 602, 603, 604, 606],
      // 商品新模型支持改门店库存和总部库存
      ...[608, 609],
      // 改重量
      ...[611],
      // 定时上下架
      ...[612, 613, 614, 615, 616],
      // 外部渠道商品
      ...[619],
      // 规格属性
      ...[620],
      // 批量改商品单据导出
      ...[621, 622],
    ],
    text: '批量任务'
  },
  classification: {
    value: [571],
    text: '商品分类'
  },
  storeGroup: {
    value: [572],
    text: '门店商品分组'
  },
  brand: {
    value: [4002],
    text: '商品品牌'
  },
  retailMinimalBrand: {
    value: [4003],
    text: '商品品牌'
  },
  retailMinimalClassification: {
    value: [605],
    text: '商品分类'
  },
  ph: {
    value: [570],
    text: '旺小店商品',
    retryUrl: WItemRetryUrl
  },
  combo: {
    value: [618],
    text: '套餐商品'
  },
  plan: {
    value: [PLAN_TASK_TYPE],
    text: '计划库存',
    queryUrl: planQueryOneUrl,
    retryUrl: PlanRetryUrl
  }
};
