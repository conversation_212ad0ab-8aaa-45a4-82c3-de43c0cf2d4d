import { isPartnerStore } from '@youzan/utils-shop';
import { transformTextMapToOptions } from '@youzan/zan-hasaki';
import { HasOfflineStoreGoodsManageAbility } from 'common/constants';

import { ProductType as commonProductType } from '../constant';
import { ProductType as StockProductType } from '../stock/constant';
import {
  // 商品库商品字段
  STORAGE_GOODS_STATUS,
  STORAGE_GOODS_CATEGORY,
  STORAGE_GOODS_NAME,
  STORAGE_PHOTO_URL,
  GOODS_SUPPLIER,
  GOODS_BRAND,
  ONLINE_GOODS_ORG,
  GOODS_LIFE_CYCLE,
  DELIVERY_TYPE,
  STORAGE_CATEGORY,
  STORAGE_CATEGORY_DESC,
  STORAGE_GOODS_NO,
  OFFLINE_CHANNEL,
  ONLINE_CHANNEL,
  SPU_SELL_CHANNEL,
  SPU_PRODUCTION_TIME,
  GOODS_ATTR,
  STORAGE_GOODS_CODE,
  STORAGE_SKU_PATTERN,
  STORAGE_INPUT_TAX_RATE,
  STORAGE_OUTPUT_TAX_RATE,
  STORAGE_SHELF_LIFE_SETTING,
  STORAGE_SUB_TITLE,
  SELL_POINT,

  // 门店商品字段
  OFFLINE_GOODS_STATUS,
  OFFLINE_GOODS_NAME,
  OFFLINE_MEMBER_DISCOUNT,
  OFFLINE_GOODS_GROUP,
  OFFLINE_SCHEDULE_DISPLAY,
  OFFLINE_BUY_LIMIT,
  OFFLINE_START_SALE_NUM,
  OFFLINE_CRUCIAL_LABEL,
  OFFLINE_GENERAL_LABEL,
  OFFLINE_PURCHASE_RIGHT_REQUEST,

  // 网店商品字段
  ONLINE_GOODS_STATUS,
  ONLINE_GOODS_GROUP,
  ONLINE_GOODS_TEMPLATE,
  ONLINE_GOODS_NAME,
  ONLINE_MEMBER_DISCOUNT,
  ONLINE_SALE_TIME,
  SCHEDULE_DISPLAY_OFF,
  ONLINE_SCHEDULE_DISPLAY,
  ONLINE_BUY_LIMIT,
  PURCHASE_RIGHT_REQUEST,
  ONLINE_DELIVERY_TEMPLATE,
  ONLINE_LOGISTICS_TIMELINESS_TEMPLATE,
  ONLINE_ORIGIN,
  ONLINE_CATEGORY,
  ONLINE_CATEGORY_DESC,
  ONLINE_AFTER_SALE,
  ONLINE_GOODS_NO,
  ONLINE_DISTRIBUTION,
  ONLINE_BARCODE,
  ONLINE_START_SALE_NUM,
  ONLINE_SALE_TYPE,
  ONLINE_SHOW_STOCK,
  ONLINE_PREPARE_TIME,
  ONLINE_GOODS_INDEX,
  ONLINE_PRODUCTION_TIME,
  ONLINE_CLASSIFICATION,
  ONLINE_BRAND,
  ONLINE_HEAVY_CONTINUED,
  ONLINE_STOCK_DEDUCTION_MODE,
  ONLINE_CRUCIAL_LABEL,
  ONLINE_GENERAL_LABEL,
  ONLINE_MESSAGES,

  // 美团外卖商品字段
  MEITUAN_GOODS_STATUS,
  MEITUAN_GOODS_GROUP,
  MEITUAN_PACKING_CHARGES,

  // 饿了么外卖商品字段
  ELEME_GOODS_STATUS,
  ELEME_GOODS_GROUP,
  ELEME_PACKING_CHARGES,

  // 美团闪购商品字段
  MEITUAN_SHANGOU_GOODS_STATUS,
  MEITUAN_SHANGOU_GOODS_GROUP,

  // 京东外卖商品字段
  JD_WM_GOODS_STATUS,
  JD_WM_GOODS_GROUP,
  JD_WM_PACKING_CHARGES
} from '../modify/fields';

/**
 * 商品类型
 */
export const ProductType = {
  Storage: [commonProductType.Storage, StockProductType.Storage],
  Offline: [commonProductType.Offline, StockProductType.Offline],
  Online: [commonProductType.Online, StockProductType.Online],
  Meituan: [commonProductType.Meituan, null],
  Eleme: [commonProductType.Eleme, null],
  MeituanShangou: [commonProductType.MeituanShangou, null],
  OfflineOrWarehouse: [commonProductType.OfflineOrWarehouse, null]
};

export const productTypeTextMp = new Map([
  [ProductType.Storage, '商品库商品'],
  [ProductType.Offline, '门店商品'],
  [ProductType.Online, '网店商品'],
]);

export const productTypeOptions = transformTextMapToOptions(productTypeTextMp).filter(item => {
  /** '门店商品'选项 根据能力进行过滤 */
  if (item.value === ProductType.Offline && !HasOfflineStoreGoodsManageAbility) {
    return false;
  }

  if (isPartnerStore) {
    return item.value !== ProductType.Storage;
  }
  return true;
});

export enum GoodsType {
  Storage = 1,
  Offline = 2,
  Online = 3,
  OnlineShop = 4,
  OfflineShop = 5,
  StorageShop = 6,
  Shop = 7,
  Meituan = 8,
  MeituanShangou = 9,
  Eleme = 10,
  OfflineOrWarehouse = 12
}

export const ProductTypeTextMap = new Map([
  [GoodsType.Storage, '商品库商品'],
  [GoodsType.Offline, '渠道商品'],
  [GoodsType.Online, '渠道商品'],
  [GoodsType.OnlineShop, '店铺商品'],
  [GoodsType.OfflineShop, '店铺商品'],
  [GoodsType.StorageShop, '商品库商品'],
  [GoodsType.Shop, '店铺商品'],
  [GoodsType.Meituan, '美团外卖商品'],
  [GoodsType.Eleme, '饿了么外卖商品'],
  [GoodsType.MeituanShangou, '美团闪购商品'],
  [GoodsType.OfflineOrWarehouse, '门店/仓库商品']
]);

export const GoodsContentOptions = [
  { text: '全部', value: '' },

  // 商品库商品字段
  { text: '修改是否可售', value: [STORAGE_GOODS_STATUS] },
  { text: '修改商品分类', value: [STORAGE_GOODS_CATEGORY] },
  { text: '修改商品名称', value: [STORAGE_GOODS_NAME, OFFLINE_GOODS_NAME, ONLINE_GOODS_NAME] },
  { text: '修改商品图片', value: [STORAGE_PHOTO_URL] },
  { text: '修改首选供应商', value: [GOODS_SUPPLIER] },
  { text: '修改商品品牌', value: [GOODS_BRAND] },
  { text: '修改店内组织', value: [ONLINE_GOODS_ORG] },
  { text: '修改生命周期', value: [GOODS_LIFE_CYCLE] },
  { text: '修改物流模式', value: [DELIVERY_TYPE] },
  { text: '修改商品类目', value: [STORAGE_CATEGORY] },
  { text: '修改商品类目描述', value: [STORAGE_CATEGORY_DESC] },
  { text: '修改商品条码', value: [STORAGE_GOODS_NO] },
  { text: '修改可售渠道', value: [SPU_SELL_CHANNEL] },
  { text: '修改加工时长', value: [SPU_PRODUCTION_TIME] },
  { text: '修改商品属性', value: [GOODS_ATTR] },
  { text: '修改商品编码', value: [STORAGE_GOODS_CODE] },
  { text: '修改规格型号', value: [STORAGE_SKU_PATTERN] },
  { text: '修改进项税率', value: [STORAGE_INPUT_TAX_RATE] },
  { text: '修改销项税率', value: [STORAGE_OUTPUT_TAX_RATE] },
  { text: '修改保质期管理', value: [STORAGE_SHELF_LIFE_SETTING] },
  { text: '修改分享描述', value: [STORAGE_SUB_TITLE] },
  { text: '修改商品卖点', value: [SELL_POINT] },

  // 门店商品字段
  { text: '修改门店商品状态', value: [OFFLINE_GOODS_STATUS] },
  { text: '修改门店商品名称', value: [OFFLINE_GOODS_NAME] },
  { text: '修改门店会员折扣', value: [OFFLINE_MEMBER_DISCOUNT] },
  { text: '修改门店商品分组', value: [OFFLINE_GOODS_GROUP] },
  { text: '修改门店定时上下架', value: [OFFLINE_SCHEDULE_DISPLAY] },
  { text: '修改门店每人限购数', value: [OFFLINE_BUY_LIMIT] },
  { text: '修改门店起售数量', value: [OFFLINE_START_SALE_NUM] },
  { text: '修改门店商品关键标签', value: [OFFLINE_CRUCIAL_LABEL] },
  { text: '修改门店商品普通标签', value: [OFFLINE_GENERAL_LABEL] },
  { text: '修改门店身份限购', value: [OFFLINE_PURCHASE_RIGHT_REQUEST] },

  // 网店商品字段
  { text: '修改网店商品状态', value: [ONLINE_GOODS_STATUS] },
  { text: '修改网店商品分组', value: [ONLINE_GOODS_GROUP] },
  { text: '修改商品页模板', value: [ONLINE_GOODS_TEMPLATE] },
  { text: '修改网店商品名称', value: [ONLINE_GOODS_NAME] },
  { text: '修改网店会员折扣', value: [ONLINE_MEMBER_DISCOUNT] },
  { text: '修改开售时间', value: [ONLINE_SALE_TIME] },
  { text: '修改定时下架', value: [SCHEDULE_DISPLAY_OFF] },
  { text: '修改网店定时上下架', value: [ONLINE_SCHEDULE_DISPLAY] },
  { text: '修改网店每人限购数', value: [ONLINE_BUY_LIMIT] },
  { text: '修改网店身份限购', value: [PURCHASE_RIGHT_REQUEST] },
  { text: '修改运费模板', value: [ONLINE_DELIVERY_TEMPLATE] },
  { text: '修改物流时效模板', value: [ONLINE_LOGISTICS_TIMELINESS_TEMPLATE] },
  { text: '修改划线价', value: [ONLINE_ORIGIN] },
  { text: '修改网店商品类目', value: [ONLINE_CATEGORY] },
  { text: '修改网店商品类目描述', value: [ONLINE_CATEGORY_DESC] },
  { text: '修改售后服务', value: [ONLINE_AFTER_SALE] },
  { text: '修改网店商品编码', value: [ONLINE_GOODS_NO] },
  { text: '修改配送方式', value: [ONLINE_DISTRIBUTION] },
  { text: '修改网店商品条码', value: [ONLINE_BARCODE] },
  { text: '修改网店起售数量', value: [ONLINE_START_SALE_NUM] },
  { text: '修改售卖方式', value: [ONLINE_SALE_TYPE] },
  { text: '修改是否展示库存', value: [ONLINE_SHOW_STOCK] },
  { text: '修改备货时间', value: [ONLINE_PREPARE_TIME] },
  { text: '修改序号', value: [ONLINE_GOODS_INDEX] },
  { text: '修改网店加工时长', value: [ONLINE_PRODUCTION_TIME] },
  { text: '修改商品分类', value: [ONLINE_CLASSIFICATION] },
  { text: '修改网店商品品牌', value: [ONLINE_BRAND] },
  { text: '修改续重收费', value: [ONLINE_HEAVY_CONTINUED] },
  { text: '修改库存扣减方式', value: [ONLINE_STOCK_DEDUCTION_MODE] },
  { text: '修改网店商品关键标签', value: [ONLINE_CRUCIAL_LABEL] },
  { text: '修改网店商品普通标签', value: [ONLINE_GENERAL_LABEL] },
  { text: '修改商品留言', value: [ONLINE_MESSAGES] },

  // 美团外卖商品字段
  { text: '修改美团外卖商品状态', value: [MEITUAN_GOODS_STATUS] },
  { text: '修改美团外卖商品分组', value: [MEITUAN_GOODS_GROUP] },
  { text: '修改美团外卖打包费', value: [MEITUAN_PACKING_CHARGES] },

  // 饿了么外卖商品字段
  { text: '修改饿了么外卖商品状态', value: [ELEME_GOODS_STATUS] },
  { text: '修改饿了么外卖商品分组', value: [ELEME_GOODS_GROUP] },
  { text: '修改饿了么外卖打包费', value: [ELEME_PACKING_CHARGES] },

  // 美团闪购商品字段
  { text: '修改美团闪购商品状态', value: [MEITUAN_SHANGOU_GOODS_STATUS] },
  { text: '修改美团闪购商品分组', value: [MEITUAN_SHANGOU_GOODS_GROUP] },

  // 京东外卖商品字段
  { text: '修改京东外卖商品状态', value: [JD_WM_GOODS_STATUS] },
  { text: '修改京东外卖商品分组', value: [JD_WM_GOODS_GROUP] },
  { text: '修改京东外卖打包费', value: [JD_WM_PACKING_CHARGES] }
];
