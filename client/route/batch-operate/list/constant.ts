import { isPartnerStore } from '@youzan/utils-shop';
import { transformTextMapToOptions } from '@youzan/zan-hasaki';
import { HasOfflineStoreGoodsManageAbility } from 'common/constants';

import { ProductType as commonProductType } from '../constant';
import { ProductType as StockProductType } from '../stock/constant';
import { OFFLINE_GOODS_NAME, ONLINE_GOODS_NAME, STORAGE_GOODS_CATEGORY, STORAGE_GOODS_NAME, STORAGE_GOODS_STATUS } from '../modify/fields';

/**
 * 商品类型
 */
export const ProductType = {
  Storage: [commonProductType.Storage, StockProductType.Storage],
  Offline: [commonProductType.Offline, StockProductType.Offline],
  Online: [commonProductType.Online, StockProductType.Online],
  Meituan: [commonProductType.Meitu<PERSON>, null],
  Eleme: [commonProductType.Eleme, null],
  MeituanShangou: [commonProductType.MeituanShangou, null],
  OfflineOrWarehouse: [commonProductType.OfflineOrWarehouse, null]
};

export const productTypeTextMp = new Map([
  [ProductType.Storage, '商品库商品'],
  [ProductType.Offline, '门店商品'],
  [ProductType.Online, '网店商品'],
]);

export const productTypeOptions = transformTextMapToOptions(productTypeTextMp).filter(item => {
  /** '门店商品'选项 根据能力进行过滤 */
  if (item.value === ProductType.Offline && !HasOfflineStoreGoodsManageAbility) {
    return false;
  }

  if (isPartnerStore) {
    return item.value !== ProductType.Storage;
  }
  return true;
});

export enum GoodsType {
  Storage = 1,
  Offline = 2,
  Online = 3,
  OnlineShop = 4,
  OfflineShop = 5,
  StorageShop = 6,
  Shop = 7,
  Meituan = 8,
  MeituanShangou = 9,
  Eleme = 10,
  OfflineOrWarehouse = 12
}

export const ProductTypeTextMap = new Map([
  [GoodsType.Storage, '商品库商品'],
  [GoodsType.Offline, '渠道商品'],
  [GoodsType.Online, '渠道商品'],
  [GoodsType.OnlineShop, '店铺商品'],
  [GoodsType.OfflineShop, '店铺商品'],
  [GoodsType.StorageShop, '商品库商品'],
  [GoodsType.Shop, '店铺商品'],
  [GoodsType.Meituan, '美团外卖商品'],
  [GoodsType.Eleme, '饿了么外卖商品'],
  [GoodsType.MeituanShangou, '美团闪购商品'],
  [GoodsType.OfflineOrWarehouse, '门店/仓库商品']
]);

export const GoodsContentOptions = [
  { text: '全部', value: '' },
  { text: '修改是否可售', value: [STORAGE_GOODS_STATUS] },
  { text: '修改商品分类', value: [STORAGE_GOODS_CATEGORY] },
  { text: '修改商品名称', value: [STORAGE_GOODS_NAME, OFFLINE_GOODS_NAME, ONLINE_GOODS_NAME] },

];
