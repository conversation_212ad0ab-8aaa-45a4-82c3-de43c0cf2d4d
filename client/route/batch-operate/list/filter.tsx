import * as React from 'react';
import { Filter } from '@youzan/retail-components';
import { OperatorSelectField, BaseSelectField } from '@youzan/biz-select-center';
import { isBranchStore, isWscSingleStore } from '@youzan/utils-shop';
import { IsRetailMinimalistOrEduV4 } from 'common/constants';

import DateRangePickerField from 'cpn/filter-fields/date-range-picker-field';

import { global } from '@youzan/retail-utils';
import { productTypeOptions } from './constant';

const { disableBatchRelease = false } = global.BUSINESS;
const { FilterRow, createFilter, FilterField } = Filter;
const CustomizeFilter = createFilter({ destroyOnUnmount: true });

const BatchFilter: React.FC<IFilterProps> = ({ onFilter, ...restProps }) => {
  const handleFilter = ({ date, ...restValues }: { date?: string[] } & IFilterValues = {}) => {
    const params = {
      ...restValues,
      startTime: date?.[0] ? `${date[0]} 00:00:00` : null,
      endTime: date?.[1] ? `${date[1]} 23:59:59` : null
    };

    onFilter(params);
  };
  const isShowProductChannels = !isBranchStore && !IsRetailMinimalistOrEduV4 && !isWscSingleStore;
  return (
    <CustomizeFilter {...restProps} shouldInitSubmitAnyway doFilterOnClear onFilter={handleFilter}>
      <FilterRow>
        <DateRangePickerField />
      </FilterRow>
      <FilterRow>
        {isShowProductChannels && (
          <BaseSelectField showTotal label="商品类型" name="channels" data={productTypeOptions} />
        )}
        
        <FilterField
          name="nameOrCode"
          component="InputField"
          label="商品筛选"
          props={{
            placeholder: '输入名称/条码/编码'
          }}
        />
        <FilterField
          name="bizBillNo"
          component="InputField"
          label="操作单号"
          props={{
            placeholder: '输入单号'
          }}
        />
      </FilterRow>
      <FilterField
        name="modifyContent"
        component="SelectField"
        label="操作内容"
        props={{
          data: [
            { text: '全部', value: '' },
            { text: '修改商品', value: 1 },
            { text: '修改商品2', value: 2 },
          ],
          tags: true,
          placeholder: '请选择操作内容'
        }}
      />
      {/* NOTE: 针对大规模连锁做白名单隐藏很不合理,PM要求这么做@新风 */}
      {!disableBatchRelease && (
        <FilterRow>
          <OperatorSelectField name="creatorId" label="操作人" />
        </FilterRow>
      )}
    </CustomizeFilter>
  );
};

export default BatchFilter;
