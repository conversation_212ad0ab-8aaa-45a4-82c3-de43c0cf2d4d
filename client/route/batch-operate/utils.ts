/* eslint-disable @youzan/yz-retail/prefer-pascal-case-const */
import { omit, differenceBy, intersectionBy, get } from 'lodash';
import { ISpuSearchData } from '@youzan/zan-hasaki';
import { SHOP_ROLE, IJoinType, isUnifiedHqStore } from '@youzan/utils-shop';
import { cachePromise } from 'common/utils';

import { Notify } from 'zent';
import { getAllStoresWithCache } from './api';

import { IBatchSubmitItemData } from './types';
import { RetailOrderManagerStockStatus, StockModel } from './constant';

export function transformDetailData<
  K extends {
    fields: {
      fieldName: string;
      fieldValue: string;
    }[];
    [key: string]: any;
  }
>(items: K[]): Pick<K, Exclude<keyof K, 'fields'>>[];

export function transformDetailData<
  K extends {
    fields: {
      fieldName: string;
      fieldValue: string;
    }[];
    [key: string]: any;
  }
>(items: K[], ifOmit: boolean): K[];

export function transformDetailData<
  K extends {
    fields: {
      fieldName: string;
      fieldValue: string;
      fieldValueDesc?: string;
    }[];
    [key: string]: any;
  }
>(items: K[], ifOmit = true): K[] | Pick<K, Exclude<keyof K, 'fields'>>[] {
  if (!Array.isArray(items)) {
    return [];
  }

  const res = items.map(item => {
    const { fields = [] } = item;
    const fieldsValueMp: Record<string, string> = {};
    const fieldsDescMp: Record<string, string> = {};

    fields.forEach(field => {
      const { fieldName, fieldValue, fieldValueDesc } = field;
      fieldValue === ''
        ? (fieldsValueMp[fieldName] = item[fieldName])
        : (fieldsValueMp[fieldName] = fieldValue);

      fieldsDescMp[fieldName] = fieldValueDesc;
    });
    // 获取商品类型
    const bizMarkCode = get(item, 'bizMark.code', '');

    if (ifOmit) {
      return {
        ...omit(item, 'fields'),
        ...fieldsValueMp,
        fieldsDescMp,
        bizMarkCode
      };
    }

    return {
      ...item,
      ...fieldsValueMp,
      fieldsDescMp,
      bizMarkCode
    };
  });
  return res;
}

// 读取文件导入的值
export function transformFileData<
  K extends {
    allFields: {
      fieldName: string;
      fieldValue: string;
    }[];
    [key: string]: any;
  }
>(items: K[]): K[] | Pick<K, Exclude<keyof K, 'allFields'>>[] {
  if (!Array.isArray(items)) {
    return [];
  }

  console.log('items', items);
  
  const res = items.map(item => {
    const { allFields = [] } = item;
    const fieldsMp: Record<string, string> = {};

    allFields.forEach((field: any) => {
      const { fieldName, fieldValue } = field;
      fieldValue === ''
        ? (fieldsMp[fieldName] = item[fieldName])
        : (fieldsMp[fieldName] = fieldValue);
    });

    return {
      ...item,
      ...fieldsMp
    };
  });

  console.log('res', res);
  
  return res;
}

/**
 * 新版选择商品处理逻辑
 *
 * @param {array} [selected=[]] 商品选择组件返回的选中商品
 * @param {[]} [current=[]] 上次已经选中的商品
 * @param {string} [key='skuId'] 商品key
 * @returns 二者并集去重，重复的以上次选中的为准
 */
export const mergeSelectGoods = (
  selected: ISpuSearchData[] = [],
  current: IBatchSubmitItemData[] = [],
  key = 'skuId'
) => {
  const newGoods = differenceBy(selected, current, key);
  const sameGoods = intersectionBy(current, selected, key);

  return [...sameGoods, ...newGoods];
};

export interface IStoreOnlyUnifiedHq {
  kdtId: number;
  isOfflineOpen: boolean;
  isOnlineOpen: boolean;
  kdtName: string;
  joinType: IJoinType;
  retailOrderManagerStock: RetailOrderManagerStockStatus;
  subOnlineStockModel: StockModel;
}

export const getAllStoreOnlyUnifiedHq: (
  params?: Record<string, any>
) => Promise<IStoreOnlyUnifiedHq[]> = async function(params) {
  if (isUnifiedHqStore) {
    return getAllStoresWithCache({
      shopRoleList: [SHOP_ROLE.BRANCH],
      ...params
    })
      .then((list: any) => {
        return list.map((store: any) => {
          return {
            isOfflineOpen: store.isOfflineOpen,
            isOnlineOpen: store.isOnlineOpen,
            kdtId: store.storeKdtId,
            kdtName: store.storeName,
            joinType: store.storeType,
            retailOrderManagerStock: store.retailOrderManagerStock,
            subOnlineStockModel: store.subOnlineStockModel
          };
        });
      })
      .catch(err => {
        Notify.error(err.msg || '查询店铺失败');
      });
  }
  return [];
};

export const getTotalOfflineChannel = cachePromise(function() {
  return getAllStoreOnlyUnifiedHq().then(list => list.filter(store => store.isOfflineOpen));
});

export const getTotalOfflineChannelIds = cachePromise(function() {
  return getTotalOfflineChannel().then(list => list.map(store => store.kdtId));
});

export const getTotalOnlineChannel = cachePromise(function() {
  return getAllStoreOnlyUnifiedHq().then(list => list.filter(store => store.isOnlineOpen));
});

export const getTotalOnlineChannelIds = cachePromise(function() {
  return getTotalOnlineChannel().then(list => list.map(store => store.kdtId));
});

// 提前预加载
getAllStoreOnlyUnifiedHq();
