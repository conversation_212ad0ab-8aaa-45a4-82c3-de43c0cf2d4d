import { SalesChannelEnum } from '@youzan/goods-domain-definitions';
import { global } from '@youzan/retail-utils';
import { isUnifiedShop, isRetailSingleStore, isWscSingleStore } from '@youzan/utils-shop';
import { transformTextMapToOptions, transformTextMapToTabs } from '@youzan/zan-hasaki';

import { IsUnityModelUnifiedShop, IsRetailMinimalistOrEduV4 } from 'common/constants';

/**
 * 单据状态
 */
export enum OrderStatus {
  /** 待提交 */
  PendingSubmit = 0,
  Completed = 60,
  Invalid = 70,
  /** 处理中 */
  Processing = 80
}

export type PartOrderStatus =
  | OrderStatus.PendingSubmit
  | OrderStatus.Completed
  | OrderStatus.Invalid;

export const orderStatusTextMap = new Map([
  [OrderStatus.PendingSubmit, '待提交'],
  [OrderStatus.Completed, '已完成'],
  [OrderStatus.Invalid, '已作废']
]);

export const newOrderStatusTextMap = new Map([[OrderStatus.Completed, '已完成']]);

export const orderStatusTabs = global.BUSINESS.shopCreateAfter20201113
  ? transformTextMapToTabs(newOrderStatusTextMap)
  : transformTextMapToTabs(orderStatusTextMap);

/**
 * 商品(规格)条码/编码
 */
export const batchCodeText =
  (isUnifiedShop && !IsUnityModelUnifiedShop) || isRetailSingleStore ? '条码' : '编码';

/**
 * 提交状态
 */
export enum CommitStatus {
  /** 保存 */
  Save,
  /** 提交 */
  Submit
}

/**
 * 操作状态
 */
export enum OperateStatus {
  /** 新建 */
  Create,
  /** 修改 */
  Update
}

/**
 * 商品操作类型
 */
export enum OperateType {
  /** 修改商品 */
  UpdateGoods = 1,
  /** 发布商品 */
  PublishGoods = 2,
  /** 修改图片 */
  Picture = 3,
  /** 修改库存 */
  Stock = 4,
  /** 修改总部库存 */
  HqStock = 5,
  /** 商品重量 */
  Weight = 6,
  /** 商品规格属性 */
  SkuProp = 7
}

/**
 * 商品类型
 */
export enum ProductType {
  Storage = 1,
  Offline,
  Online,
  Shop = 7,
  Meituan = 8,
  MeituanShangou = 9,
  Eleme = 10,
  OfflineOrWarehouse = 12,
  Channel = 99,
  JdWm = 13,
}

export const HandleChannelsMap = new Map([
  [ProductType.Online, SalesChannelEnum.OnlineChannelId],
  [ProductType.Offline, SalesChannelEnum.OfflineChannelId],
  [ProductType.Meituan, SalesChannelEnum.MtWmChannelId],
  [ProductType.Eleme, SalesChannelEnum.ElemeChannelId],
  [ProductType.MeituanShangou, SalesChannelEnum.MtShanGouChannelId],
  [ProductType.JdWm, SalesChannelEnum.JdChannelId]
])

export enum NewProcessProductType {
  Storage = 13,
  Channel = 14
}

export type FieldProductType =
  | ProductType.Storage
  | ProductType.Offline
  | ProductType.Online
  | ProductType.Meituan
  | ProductType.Eleme
  | ProductType.MeituanShangou
  | ProductType.JdWm;

export const productTypeTextMp = new Map([
  [ProductType.Storage, '商品库商品'],
  [ProductType.Offline, '门店商品'],
  [ProductType.Online, '网店商品'],
  [ProductType.Channel, '渠道商品'],
  [ProductType.Meituan, '美团外卖商品'],
  [ProductType.Eleme, '饿了么外卖商品'],
  [ProductType.MeituanShangou, '美团闪购商品']
]);

export const ChannelTextMap = new Map([
  [ProductType.Offline, '门店渠道'],
  [ProductType.Online, '网店渠道'],
  [ProductType.Shop, '门店渠道'],
  [ProductType.Meituan, '美团外卖'],
  [ProductType.Eleme, '饿了么外卖'],
  [ProductType.MeituanShangou, '美团闪购'],
  [ProductType.JdWm, '京东外卖']
]);

/**
 * 商品来源
 */
export enum ProductOrigin {
  All = 0,
  HqCreate,
  OnlineCreate
}

export const ProductOriginTextMp = new Map([
  [ProductOrigin.All, '全部商品'],
  [ProductOrigin.HqCreate, '总部创建'],
  [ProductOrigin.OnlineCreate, '网店创建']
]);

export const goodsOriginSearchMp = {
  [ProductOrigin.All]: {},
  [ProductOrigin.HqCreate]: {
    createdType: 0
  },
  [ProductOrigin.OnlineCreate]: {
    createdType: 1
  }
};

export const goodsOriginGroupMp = {
  [ProductOrigin.All]: {},
  [ProductOrigin.HqCreate]: {
    type: 0
  },
  [ProductOrigin.OnlineCreate]: {
    type: 2
  }
};

/**
 * 店铺类型
 */
export enum ShopType {
  Singe = 1,
  HqShop,
  OfflineShop,
  OnlineShop
}

/**
 * 导出什么状态的单据
 */
export enum ExecuteStatus {
  Success = 20,
  Fail = 30
}

export const executeStatusTextMap = new Map([
  [ExecuteStatus.Success, '成功'],
  [ExecuteStatus.Fail, '失败']
]);

export const executeStatusOptions = transformTextMapToOptions(executeStatusTextMap);

export const executeStatusTabs = transformTextMapToTabs(executeStatusTextMap);

// 支持保存草稿店铺
export const IsSupportSaveShop = !(
  IsUnityModelUnifiedShop ||
  IsRetailMinimalistOrEduV4 ||
  isWscSingleStore
);

/**
 * 单据明细列表的查询状态
 */
export enum ExecuteQueryStatus {
  QueryDetail,
  UpdateDetail
}

// 商品类型枚举
export enum GoodsType {
  NormalGoods = 0,
  DiscountGoods = 1,
  FoodGoods = 5,
  FenXiaoGoods = 10,
  MemberGoods = 20,
  GiftGoods = 21,
  YouzanMettingGoods = 23,
  CycleBuyGoods = 24,
  WholesaleGoods = 25,
  VirtualGoods = 30,
  KnowledgePayGoods = 31,
  HotelGoods = 35,
  NormalServiceGoods = 40,
  NormalVirtualGoods = 82,
  ECardGoods = 83,
  NormalSpecialVirtualGoods = 182,
  ECardSpecialGoods = 183,
  OutsideMemberGoods = 201,
  OutsideCashGoods = 202,
  OutsideNormalGoods = 203,
  MockNotExitGoods = 205,
  AppletsQrcodeGoods = 206
}

/**
 * 规格条码 key 枚举
 * 商品库用的是 skuNo 字段 表示规格条码，网店商品用 barcode 字段表示规格条码
 */
export enum SpecBarcode {
  Library = 'skuNo',
  Online = 'barcode'
}

/**
 * 查询扩展参数
 * 1：商品基础信息
 * 2：库存信息
 * 3：商品关联关系信息
 * 4：商品同步信息
 * 5：分类关联分组信息
 * 6：关联的总部网店商品库存信息
 */
export enum Attributes {
  ShopBase = 1,
  Stock = 2,
  HqStock = 6
}

/**
 * 使用进出存管理
 */
export enum RetailOrderManagerStockStatus {
  Disable = 0,
  Enable = 1
}

/**
 * 库存模式
 * 1：独立销售库存
 * 2：共享总部库存
 * 3：共享门店/仓库库存
 * -1：其他来源
 */
export enum StockModel {
  OnMyOwn = 1,
  ShareHeadquarters = 2,
  ShareOfflineShopOrWarehouse = 3,
  Other = -1
}
