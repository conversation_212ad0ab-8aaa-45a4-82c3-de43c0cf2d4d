import * as React from 'react';
import { useMemo } from 'react';
import { IGridColumn } from 'zent';
import { GoodsCell } from '@youzan/retail-components';
import { GoodsType } from '@youzan/zan-hasaki';
import { isWscSingleStore } from '@youzan/utils-shop';
import { GoodsSelectorBtn, channelEnum, IObj } from '@youzan/biz-goods-selector';
import { EnumMarkCode, SalesChannelEnum } from '@youzan/goods-domain-definitions';

import BatchImportButton from 'cpn/batch-import-button';
import createImportDialog from 'cpn/goods-selection/import';
import {
  IsUnityModelWithoutFwh,
  IsRetailMinimalistOrEduV4,
  ShowBakeTypeOption,
  GoodsType as CommonGoodsType
} from 'common/constants';
import { global } from '@youzan/retail-utils';

import getMoreSearchConfig from 'common/utils/get-more-search-config';
import { optimizeExcelUpload, upload } from 'cpn/file-uploader/api';
import { get } from 'lodash';
import { goodsTypeSearchMp, goodsChannelMp, goodsTypeMap } from './constant';
import {
  OperateType,
  ProductType,
  ProductOrigin,
  goodsOriginSearchMp,
  goodsOriginGroupMp,
  FieldProductType
} from '../constant';
import { useOrgScope } from '../hooks';
import { isHqStoreAndNewModel } from 'common/constants/data-permissions';
import { getProductType } from './index';

const { BUSINESS } = global;
const { isShopOrgSupported, supportOptimizeExcel = true } = BUSINESS;

interface IActionProps {
  disabled: boolean;
  type: FieldProductType;
  origin: ProductOrigin;
  fields: string[];
  items: Record<string, any>[];
  onGoodsChange: (items: Record<string, any>[]) => void;
  onImportSuccess: (items: Record<string, any>[]) => void;
}

function Action(props: IActionProps) {
  const { type, origin, fields, items, disabled, onGoodsChange, onImportSuccess } = props;

  const isExternalChannel = [
    ProductType.Meituan,
    ProductType.Eleme,
    ProductType.MeituanShangou
  ].includes(type);

  const openImportModal = () => {
    const commonParams = {
      productOrigin: origin,
      productType: getProductType(type),
      operateType: OperateType.UpdateGoods
    };

    createImportDialog({
      accept: '.xlsx, .xls',
      templateParams: {
        ...commonParams
      },
      importParams: {
        ...commonParams,
        seletedModifyField: fields
      },
      onImportSuccess,
      templateText: '下载批量修改模板',
      templateUrl: '/youzan.retail.product.batch.modify.query.import/1.0.0/template',
      importUrl: '/youzan.retail.product.batch.modify.async/1.0.0/import',
      batchKeysUrl: '/youzan.retail.product.batch.query.importdatakeys/1.0.0',
      batchDataUrl: '/youzan.retail.product.batch.query.importdata/1.0.0',
      onUpload: (token, files) =>
        supportOptimizeExcel ? optimizeExcelUpload(token, files) : upload(token, files)
    });
  };

  const moreSearchConfig = useMemo(() => {
    return type === ProductType.Online ? getMoreSearchConfig() : [];
  }, [type]);

  const handleListColumns = (columns: IGridColumn[]) => {
    const [checkbox, , skuColumn, ...column] = columns;
    const baseColumns = [
      checkbox,
      {
        title: '商品',
        width: 250,
        bodyRender: (data: IObj) => {
          /** 高级版中，商品库spu条码spuNo，网店商品spu条码barcode */
          data.spuNo = data.barcode || data.spuNo;
          return <GoodsCell {...data} name={data.name || data.title} showSpu />;
        }
      },
      skuColumn
    ];
    if (isExternalChannel) {
      return [
        checkbox,
        {
          title: '商品',
          width: 250,
          bodyRender: (data: IObj) => {
            return <GoodsCell {...data} name={data.itemName} spuNo={data.channelItemNo} showSpu />;
          }
        },
        // 空的 column 用来占位
        {
          title: '',
          bodyRender: () => <div />
        }
        // skuColumn
      ];
    }
    if (IsUnityModelWithoutFwh) {
      return [...baseColumns, ...column];
    }
    if (IsRetailMinimalistOrEduV4) {
      return [
        ...baseColumns,
        {
          title: '类型',
          width: 250,
          bodyRender: (data: IObj) => {
            return goodsTypeMap.get(data.goodsType);
          }
        },
        ...column
      ];
    }
    return columns;
  };

  const handleSkuColumns = (columns: IGridColumn[]) => {
    if (IsUnityModelWithoutFwh && (type === ProductType.Online || isExternalChannel)) {
      return columns.map(column =>
        column.name === 'skuNo'
          ? {
              title: '规格条码',
              name: 'barcode'
            }
          : column
      );
    }
    return columns;
  };

  const otherListParams: {
    itemTypes?: Array<GoodsType>;
    includeBizCodes?: Array<EnumMarkCode>;
  } = {};

  // 新商品模型-修改网店商品时，支持修改实物商品和分销商品
  if (IsUnityModelWithoutFwh && type === ProductType.Online) {
    otherListParams.itemTypes = [GoodsType.Actual, GoodsType.FenXiao];
    // 支持搜索海淘分销商品
    otherListParams.includeBizCodes = [EnumMarkCode.Haitao];
  }

  const [shopOrgIds] = useOrgScope();

  const externalChannel = useMemo(() => {
    if (type === ProductType.Meituan) {
      return SalesChannelEnum.MtWmChannelId;
    }
    if (type === ProductType.Eleme) {
      return SalesChannelEnum.ElemeChannelId;
    }
    if (type === ProductType.MeituanShangou) {
      return SalesChannelEnum.MtShanGouChannelId;
    }
    return null;
  }, [type]);

  return (
    <div className="action-container" style={{ display: 'flex' }}>
      <GoodsSelectorBtn
        needQueryOne={!isExternalChannel}
        onlyShowMultiSpec
        /**
         * 组件内部使用 lodash.merge 合并 props.listParams 和 state.searchParams
         * 搜索参数可能会出现，内外同时指定了同一个字段。导致 merge 出错，因此使用 handleSearchParams 构建参数
         *
         * @example
         * listParams: { isDisplays: [0, 1] }
         * searchParams: { isDisplays: [0] }
         * return: { isDisplays: [0, 1] }
         * except: { isDisplays: [0] }
         */
        handleSearchParams={params => {
          if (isExternalChannel) {
            const { pageNo, pageSize, nameOrSkuNo, externalGroup } = params;
            return {
              ...goodsTypeSearchMp[type],
              page: pageNo,
              pageSize,
              nameOrNo: nameOrSkuNo,
              groupId: externalGroup?.at(-1),
              selectionComponent: true
            };
          }
          return {
            ...goodsTypeSearchMp[type],
            ...params,
            ...goodsOriginSearchMp[origin],
            isSupportType: isWscSingleStore,
            shopOrgIds: isShopOrgSupported ? shopOrgIds : [], // 无能力 传空查询全店
            ...otherListParams
          };
        }}
        groupParams={{
          ...goodsOriginGroupMp[origin]
        }}
        channel={isHqStoreAndNewModel ? channelEnum.product : goodsChannelMp[type] || channelEnum.storage}
        externalChannel={externalChannel}
        value={items}
        handleConfirm={onGoodsChange}
        disabled={disabled}
        pageSize={moreSearchConfig.length > 0 ? 50 : 20}
        moreSearchConfig={moreSearchConfig}
        handleListColumns={handleListColumns}
        handleSkuColumns={handleSkuColumns}
        defineGoodsTypeOptions={goodsTypeOptions => {
          if (!ShowBakeTypeOption) {
            return goodsTypeOptions.filter(option => option.value !== CommonGoodsType.CakeGoods);
          }
          return goodsTypeOptions;
        }}
        handleListData={items => {
          if (isExternalChannel) {
            return Promise.resolve(
              items.map((data: any) => ({
                ...data,
                isNonSpec: !data.hasMultiSku
              }))
            );
          }

          return Promise.resolve(items);
        }}
        handleTransformQueryOneResult={data => {
          if (isExternalChannel) {
            return {
              ...data,
              stock: get(data, 'itemStockParam.stock', []).map((item: any) => {
                return {
                  ...item,
                  specs: get(item, 'itemSkuAssist.sku')
                };
              })
            };
          }
          return data;
        }}
      />
      <BatchImportButton
        disabled={(items || []).length > 0 || disabled}
        handleClick={openImportModal}
      />
    </div>
  );
}

export default Action;
