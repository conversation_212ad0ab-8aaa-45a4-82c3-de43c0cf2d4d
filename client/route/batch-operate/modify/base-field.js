import React, { useEffect, useMemo, useState } from 'react';
import { get } from 'lodash';
import { Checkbox, Icon, Pop, Radio, Tabs } from 'zent';
import cx from 'classnames';
import { Field } from '@youzan/retail-form';
import {
  isOnlineBranchStore,
  isRetailSingleStore,
  isUnifiedHqStore,
  isPartnerStore
} from '@youzan/utils-shop';
import { CardWrapper, PopWrapper } from '@youzan/retail-components';
import { SalesChannelEnum } from '@youzan/goods-domain-pc-components';
import RemarkField from 'cpn/field/remark-field';
import { IsUnityModelWithoutFwh, HasMultiChannelProductManageAbility } from 'common/constants';

import { isHqStoreAndNewModel } from 'common/constants/data-permissions';
import { GOODS_TYPE_SELECT_DATA, PRODUCT_ORIGIN_SELECT_DATA, ChannelSelectData } from './constant';
import { ProductType, productTypeTextMp } from '../constant';
import { queryTrip<PERSON>hannelBind } from './api';
import { GOODS_FIELDS_MAP } from './fields';

import style from './index.scss';

const renderFields = fields => {
  if (get(fields, 'length', 0) === 0) {
    return null;
  }

  return fields.map(({ text, value, helpDesc, disabled = false }) => {
    if (disabled) {
      return (
        <PopWrapper shouldWrap={Boolean(helpDesc)} content={helpDesc} trigger="hover">
          <Checkbox className="mb6" key={value} value={value} disabled>
            <div className={style.checkItems}>
              <span className="item-text">{text}</span>
            </div>
          </Checkbox>
        </PopWrapper>
      );
    }
    return (
      <Checkbox className="mb6" key={value} value={value}>
        <div className={style.checkItems}>
          <span className="item-text">{text}</span>
          {helpDesc && (
            <Pop trigger="hover" content={helpDesc} position="auto-top-center">
              <Icon type="help-circle" style={{ color: '#C8C9CC' }} />
            </Pop>
          )}
        </div>
      </Checkbox>
    );
  });
};

// 渠道选择修改组件
const ChannelModifySelector = ({
  input: { value = {}, onChange } = {},
  disabled,
  defaultActiveChannel
}) => {
  const [activeChannel, setActiveChannel] = useState(
    defaultActiveChannel || ChannelSelectData[0]?.value || ProductType.Online
  );

  // 获取当前渠道的修改项数量
  const getChannelFieldsCount = channelType => {
    const channelFields = value[channelType] || [];
    return channelFields.length;
  };

  // 获取当前渠道的可选修改项
  const getCurrentChannelFields = channelType => {
    const fields = GOODS_FIELDS_MAP[channelType] || [];
    // 过滤掉隐藏的字段
    return fields.filter(field => !field.isHide);
  };

  // 处理修改项变化
  const handleFieldsChange = (channelType, selectedFields) => {
    const newValue = {
      ...value,
      [channelType]: selectedFields
    };

    onChange(newValue);
  };

  // 渲染Tab标签
  const renderTabLabel = channel => {
    const count = getChannelFieldsCount(channel.value);
    return (
      <span>
        {channel.text}
        {count > 0 && <span className={style.channelCount}>({count})</span>}
      </span>
    );
  };

  return (
    <div className={style.channelModifySelector}>
      <Tabs activeId={activeChannel} onChange={setActiveChannel} type="card">
        {ChannelSelectData.map(channel => (
          <Tabs.TabPanel key={channel.value} tab={renderTabLabel(channel)} id={channel.value}>
            <div className={style.channelFieldsWrapper}>
              <div className="zent-checkbox-group">
                {getCurrentChannelFields(channel.value).map(field => {
                  const isSelected = (value[channel.value] || []).includes(field.value);
                  return (
                    <Checkbox
                      key={field.value}
                      className="mb6"
                      value={field.value}
                      checked={isSelected}
                      disabled={disabled || field.disabled}
                      onChange={e => {
                        const currentFields = value[channel.value] || [];
                        let newFields;
                        if (e.target.checked) {
                          newFields = [...currentFields, field.value];
                        } else {
                          newFields = currentFields.filter(f => f !== field.value);
                        }
                        handleFieldsChange(channel.value, newFields);
                      }}
                    >
                      <div className={style.checkItems}>
                        <span className="item-text">{field.text}</span>
                        {field.helpDesc && (
                          <Pop trigger="hover" content={field.helpDesc} position="auto-top-center">
                            <Icon type="help-circle" style={{ color: '#C8C9CC' }} />
                          </Pop>
                        )}
                      </div>
                    </Checkbox>
                  );
                })}
              </div>
            </div>
          </Tabs.TabPanel>
        ))}
      </Tabs>
    </div>
  );
};

const BaseField = props => {
  const { type, isEdit, origin, fields, allFields, defaultActiveChannel } = props;

  const [bind, setBind] = useState({
    isBindMtWm: false,
    isBindEleme: false,
    isBindMtShanGou: false
  });

  const getBindExternalStatus = (items, id) => {
    return !!items.find(({ bindedChannelIdList = [] }) => bindedChannelIdList.includes(id));
  };

  useEffect(() => {
    // 新商品模型-非多渠道查询渠道绑定情况
    if (!HasMultiChannelProductManageAbility && IsUnityModelWithoutFwh) {
      queryTripChannelBind().then(items => {
        setBind({
          isBindMtWm: getBindExternalStatus(items, SalesChannelEnum.MtWmChannelId),
          isBindEleme: getBindExternalStatus(items, SalesChannelEnum.ElemeChannelId),
          isBindMtShanGou: getBindExternalStatus(items, SalesChannelEnum.MtShanGouChannelId)
        });
      });
    }
  }, []);

  const goodsTypeList = useMemo(() => {
    const finalGoodsTypeList = [...GOODS_TYPE_SELECT_DATA];
    const { isBindMtWm, isBindEleme, isBindMtShanGou } = bind;
    if (isBindMtWm) {
      finalGoodsTypeList.push({
        text: productTypeTextMp.get(ProductType.Meituan),
        value: ProductType.Meituan
      });
    }

    if (isBindEleme) {
      finalGoodsTypeList.push({
        text: productTypeTextMp.get(ProductType.Eleme),
        value: ProductType.Eleme
      });
    }

    if (isBindMtShanGou) {
      finalGoodsTypeList.push({
        text: productTypeTextMp.get(ProductType.MeituanShangou),
        value: ProductType.MeituanShangou
      });
    }

    return finalGoodsTypeList;
  }, [bind]);

  return (
    <CardWrapper title="基本信息">
      <div
        className={cx('mb20', {
          [style.goodsTypeHide]: !(isRetailSingleStore || isUnifiedHqStore || isPartnerStore)
        })}
      >
        <Field
          name="type"
          label="商品类型："
          component="SelectField"
          disabled={isEdit || isOnlineBranchStore}
          onChange={props.resetForm}
          props={{
            data: goodsTypeList,
            type: 'rc'
          }}
        />
      </div>
      {!isHqStoreAndNewModel &&
        HasMultiChannelProductManageAbility &&
        [
          ProductType.Online,
          ProductType.Offline,
          ProductType.Meituan,
          ProductType.Eleme,
          ProductType.MeituanShangou
        ].includes(type) && (
          <Field
            name="channel"
            label="销售渠道："
            component="RadioGroupField"
            onChange={props.resetForm}
          >
            {ChannelSelectData.map(item => (
              <Radio value={item.value} key={item.value}>
                {item.text}
              </Radio>
            ))}
          </Field>
        )}
      {/* 商品统一模型下，所有商品都是总部创建，BU 不可自建商品，不需要商品来源 */}
      {isOnlineBranchStore && !IsUnityModelWithoutFwh && (
        <Field
          name="origin"
          component="SelectField"
          label="商品来源："
          disabled={isEdit}
          value={origin}
          onChange={props.resetForm}
          props={{
            data: PRODUCT_ORIGIN_SELECT_DATA,
            type: 'rc'
          }}
        />
      )}
      {(!isHqStoreAndNewModel || type === ProductType.Storage) && (
        // 原有的单一修改项选择
        <Field
          name="fields"
          disabled={isEdit}
          label="选择修改："
          value={fields}
          className={style.checkboxWrapper}
          validate={[
            {
              type: 'required',
              message: '请选择修改项'
            }
          ]}
          component="CheckboxGroupField"
        >
          {renderFields(allFields)}
        </Field>
      )}
      {isHqStoreAndNewModel &&
        HasMultiChannelProductManageAbility &&
        [
          ProductType.Online,
          ProductType.Offline,
          ProductType.Meituan,
          ProductType.Eleme,
          ProductType.MeituanShangou,
          ProductType.JdWm
        ].includes(type) && (
          <Field
            name="channelFields"
            disabled={isEdit}
            label="选择修改："
            className={style.checkboxWrapper}
            validate={[
              {
                type: 'validator',
                rule: (value = {}) => {
                  const hasSelectedFields = Object.values(value).some(
                    fields => fields && fields.length > 0
                  );
                  return hasSelectedFields ? undefined : '请选择修改项';
                }
              }
            ]}
            defaultActiveChannel={defaultActiveChannel}
            component={ChannelModifySelector}
          />
        )}
      <div className={style.checkboxWrapper} />
      <RemarkField />
    </CardWrapper>
  );
};

export default BaseField;
