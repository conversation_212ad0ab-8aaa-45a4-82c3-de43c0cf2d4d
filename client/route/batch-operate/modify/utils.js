import { difference, keys, isArray, isNil, get, uniq, isUndefined, omit } from 'lodash';
import {
  formatDatetime,
  transformValByRatio,
  times,
  global,
  deepJsonParse
} from '@youzan/retail-utils';
import { isHqStore } from '@youzan/utils-shop';
import { InventoryType, GoodsMeasurement, GoodsType } from '@youzan/zan-hasaki';
import {
  parseScheduleShelfResponse,
  transformScheduleShelfData,
  ScheduleShelfType
} from '@youzan/goods-domain-pc-components';
import {
  FetchCategoryMethod,
  GoodsChannel,
  IsPartial,
  IsMU,
  HasOfflineAbility,
  OrgSupportGoodType,
  IsUnityModelWithoutFwh
} from 'common/constants';
import { isSupportProductionTimeCategory } from 'common/fns';
import { buildDistributionSearchParams } from 'cpn/delivery-mode';

import {
  Fields as RenameFields,
  Rules as RenameRules
} from 'cpn/field/batch-operate/batch-rename/constants';
import { IMMEDIATE_SALE, TIMING_SALE } from 'cpn/field/batch-operate/sale-time-field/cpn';
import { BuyLimitType } from 'cpn/field/batch-operate/buy-limit-field/constant';
import {
  HaitaoMaxSendDays,
  MaxSendDays,
  SaleType
} from 'cpn/field/batch-operate/online-sale-type/constant';
import { GroupModifyType } from 'cpn/field/batch-operate/online-goods-group/constants';
import {
  SAME_DELIVERY,
  DELIVERY_TEMPLATE,
  EXPRESS_PAY_MODE
} from 'cpn/field/batch-operate/delivery-template-field/cpn';
import {
  BRANCH_TEMPLATE_ID,
  HQ_TEMPLATE_ID
} from 'cpn/field/batch-operate/delivery-template-field/delivery-template-select';
import { splitSellChannelPartial, combineSellChannelPartial } from 'common/utils';

import { AfterSale } from 'cpn/field/batch-operate/online-after-sale/constants';
import { UpdateType } from 'cpn/field/batch-operate/goods-attr/types';
import { SalesChannelEnum } from '@youzan/goods-domain-definitions';
import { isHqStoreAndNewModel } from 'common/constants/data-permissions';
import {
  STORAGE_GOODS_STATUS,
  STORAGE_GOODS_CATEGORY,
  STORAGE_GOODS_NAME,
  OFFLINE_GOODS_NAME,
  STORAGE_CATEGORY,
  STORAGE_GOODS_NO,
  ONLINE_GOODS_NAME,
  GOODS_SUPPLIER,
  GOODS_LIFE_CYCLE,
  OFFLINE_GOODS_STATUS,
  OFFLINE_MEMBER_DISCOUNT,
  OFFLINE_GOODS_GROUP,
  ONLINE_MEMBER_DISCOUNT,
  ONLINE_GOODS_GROUP,
  GOODS_BRAND,
  ONLINE_GOODS_ORG,
  ONLINE_GOODS_STATUS,
  ONLINE_GOODS_TEMPLATE,
  ONLINE_SALE_TIME,
  ONLINE_BUY_LIMIT,
  ONLINE_DELIVERY_TEMPLATE,
  ONLINE_LOGISTICS_TIMELINESS_TEMPLATE,
  OFFLINE_CHANNEL,
  ONLINE_CHANNEL,
  SPU_SELL_CHANNEL,
  ONLINE_ORIGIN,
  ONLINE_CATEGORY,
  ONLINE_CATEGORY_DESC,
  ONLINE_GOODS_NO,
  ONLINE_DISTRIBUTION,
  ONLINE_AFTER_SALE,
  ONLINE_BARCODE,
  ONLINE_SHOW_STOCK,
  ONLINE_START_SALE_NUM,
  ONLINE_SALE_TYPE,
  ONLINE_PREPARE_TIME,
  SPU_PRODUCTION_TIME,
  ONLINE_PRODUCTION_TIME,
  SCHEDULE_DISPLAY_OFF,
  STORAGE_GOODS_CODE,
  ONLINE_CLASSIFICATION,
  ONLINE_BRAND,
  GOODS_ATTR,
  ONLINE_HEAVY_CONTINUED,
  OFFLINE_SCHEDULE_DISPLAY,
  ONLINE_SCHEDULE_DISPLAY,
  SELL_POINT,
  PURCHASE_RIGHT_REQUEST,
  OFFLINE_BUY_LIMIT,
  OFFLINE_START_SALE_NUM,
  MEITUAN_GOODS_GROUP,
  ELEME_GOODS_GROUP,
  MEITUAN_SHANGOU_GOODS_GROUP,
  ONLINE_GOODS_INDEX,
  STORAGE_INPUT_TAX_RATE,
  STORAGE_OUTPUT_TAX_RATE
} from './fields';
import { getTotalOfflineChannelIds, getTotalOnlineChannelIds } from '../utils';
import { ProductType } from '../constant';
import { ChannelSelectData, goodsApiField, goodsBizMarkCodeMap } from './constant';
import { queryLoadProductBatchStatus, queryBatchOrderDetail } from '../api';

const { KDT_ID } = global;

// 获取运费类型
const getDeliveryType = data => {
  if (data.expressPayMode === 1) {
    return EXPRESS_PAY_MODE;
  }
  if (data.deliveryTemplateId) {
    return DELIVERY_TEMPLATE;
  }
  return SAME_DELIVERY;
};

const transformGoodsField = config => {
  const transformItem = data => {
    const result = {};
    keys(config).forEach(key => {
      const transform = config[key];
      if (typeof transform === 'string') {
        result[key] = data[transform];
      } else if (typeof transform === 'function') {
        result[key] = transform(data);
      }
    });

    return result;
  };

  return data => {
    if (isArray(data)) {
      return data.map(item => {
        return {
          ...item,
          ...transformItem(item)
        };
      });
    }
    return {
      ...data,
      ...transformItem(data)
    };
  };
};

const getParentId = deliveryTemplateInfo => {
  const kdtId = get(deliveryTemplateInfo, 'kdtId');
  if (kdtId === KDT_ID) {
    return BRANCH_TEMPLATE_ID;
  }
  return HQ_TEMPLATE_ID;
};

/** 获取门店商品分组（组件值） */
const getOfflineGroupValue = groupModels => ({
  items: groupModels,
  ids: groupModels?.map(item => item.groupId)
});

/** 获取身份限购 */
const PurchaseRightTransField = {
  [PURCHASE_RIGHT_REQUEST]: ({ purchaseRight, purchaseRightVO = {} }) => {
    const umpLevel = [];
    const umpLevelName = [];
    const umpRealLevel = [];
    const umpRealLevelName = [];
    const umpTags = [];
    const umpTagsName = [];
    if (Array.isArray(purchaseRightVO.purchaseRightLevelVOS)) {
      purchaseRightVO.purchaseRightLevelVOS.forEach(item => {
        umpLevel.push(item.id);
        umpLevelName.push(item.name);
      });
    }
    if (Array.isArray(purchaseRightVO.purchaseRightRealLevelVOS)) {
      purchaseRightVO.purchaseRightRealLevelVOS.forEach(item => {
        umpRealLevel.push(item.id);
        umpRealLevelName.push(item.name);
      });
    }
    if (Array.isArray(purchaseRightVO.purchaseRightTagVOS)) {
      purchaseRightVO.purchaseRightTagVOS.forEach(item => {
        umpTags.push(item.id);
        umpTagsName.push(item.name);
      });
    }
    return {
      purchaseRight,
      umpLevel,
      umpLevelName,
      umpRealLevelName,
      umpRealLevel,
      umpTags,
      umpTagsName
    };
  }
};

// 网店选择和导入公共修改项默认值转换
const CommonOnlineTransFields = {
  [ONLINE_GOODS_NAME]: 'title',
  [ONLINE_GOODS_TEMPLATE]: ({ componentsExtraId }) => {
    // 普通版(-1)、简洁流畅版(-2)不支持批量设置
    if (componentsExtraId === -1 || componentsExtraId === -2) {
      return null;
    }

    return componentsExtraId;
  },
  [ONLINE_GOODS_STATUS]: isHqStore
    ? undefined
    : ({ isDisplay }) => {
        return {
          display: isDisplay
        };
      },
  [ONLINE_MEMBER_DISCOUNT]: ({ joinLevelDiscount }) => {
    return joinLevelDiscount === 1;
  },
  [ONLINE_SALE_TIME]: ({ startSoldTime }) => {
    if (startSoldTime === 0) {
      return {
        isTimingSold: IMMEDIATE_SALE
      };
    }

    return {
      isTimingSold: TIMING_SALE,
      saleTime: formatDatetime(times(startSoldTime, 1000))
    };
  },
  ...PurchaseRightTransField,
  [ONLINE_BUY_LIMIT]: ({ itemMarkAggregateModel, quota: quotaNum }) => {
    const { quotaMark } = itemMarkAggregateModel || {};
    // 周期限购
    if (quotaMark) {
      const { quotaCycle, quota } = quotaMark;
      const quotaTypeList = [
        BuyLimitType.Always,
        BuyLimitType.Day,
        BuyLimitType.Weekly,
        BuyLimitType.Monthly,
        BuyLimitType.OrderQuota
      ];
      return {
        buyLimitType: quotaTypeList[+quotaCycle],
        buyLimitNum: quota
      };
    }

    // 终身限购
    if (quotaNum > 0) {
      return {
        buyLimitType: BuyLimitType.Always,
        buyLimitNum: quotaNum
      };
    }

    // 不限购
    return {
      buyLimitType: BuyLimitType.NoQuota
    };
  },
  [ONLINE_GOODS_INDEX]: 'num',
  [ONLINE_GOODS_GROUP]: 'groupModels',
  [ONLINE_ORIGIN]: 'origin',
  [ONLINE_GOODS_NO]: 'goodsNo',
  [ONLINE_DISTRIBUTION]: ({ itemMarkAggregateModel }) => {
    return itemMarkAggregateModel?.distributionMark ?? {};
  },
  [ONLINE_HEAVY_CONTINUED]: ({ itemMarkAggregateModel }) => {
    return itemMarkAggregateModel?.distributionMark?.heavyContinued;
  },
  [ONLINE_AFTER_SALE]: ({ itemMarkAggregateModel }) => {
    const { unconditionalReturnMark, barterMark, quickRefundMark } = itemMarkAggregateModel || {};
    return {
      isSupportBarter: barterMark?.isSupportBarter,
      onlineSupportUnconditionalReturn: unconditionalReturnMark?.supportUnconditionalReturn,
      supportQuickRefund: quickRefundMark?.supportQuickRefund
    };
  },
  [ONLINE_SHOW_STOCK]: 'hideStock',
  [ONLINE_START_SALE_NUM]: ({ itemMarkAggregateModel }) => {
    return itemMarkAggregateModel?.startSaleNumMark?.startSaleNum;
  },
  [OFFLINE_START_SALE_NUM]: ({ itemMarkAggregateModel }) => {
    return itemMarkAggregateModel?.startSaleNumMark?.startSaleNum;
  },
  [ONLINE_SALE_TYPE]: ({ preSaleInfo, itemMarkAggregateModel }) => {
    const { preSaleMark } = itemMarkAggregateModel || {};
    if (preSaleInfo) {
      const { etdStart, etdType, etdDays } = preSaleInfo;
      return {
        etdType,
        etdDays,
        preSaleType: SaleType.PreSale,
        etdStart: etdStart ? new Date(etdStart).getTime() : undefined
      };
    }

    return {
      preSaleType: preSaleMark?.preSaleType === 1 ? SaleType.DepositPreSale : SaleType.CashSale
    };
  },
  [ONLINE_PREPARE_TIME]: ({
    isNonSpec,
    itemSkuMarkAggregateModelList: skuModelList,
    itemMarkAggregateModel: spuModel
  }) => {
    if (isNonSpec) {
      return spuModel?.prepareTimeMark?.prepareTime;
    }

    return skuModelList?.length === 1 ? skuModelList[0].prepareTimeMark?.prepareTime : null;
  },
  [SCHEDULE_DISPLAY_OFF]: ({ itemMarkAggregateModel }) => {
    if (IsMU) {
      return {
        enableScheduleDisplayOff: undefined
      };
    }
    const scheduleDisplayOffTime = itemMarkAggregateModel?.itemDisplayMark?.scheduleDisplayOffTime;

    return {
      enableScheduleDisplayOff: scheduleDisplayOffTime > 0 ? 1 : 0,
      scheduleDisplayOffTime: scheduleDisplayOffTime > 0 ? scheduleDisplayOffTime : undefined
    };
  },
  [ONLINE_SCHEDULE_DISPLAY]: ({ itemMarkAggregateModel }) =>
    isHqStore
      ? {
          type: ScheduleShelfType.All
        }
      : parseScheduleShelfResponse(
          itemMarkAggregateModel?.itemDisplayMark?.scheduleDisplayModel ?? {}
        ),
  [ONLINE_LOGISTICS_TIMELINESS_TEMPLATE]: ({ itemMarkAggregateModel }) => {
    const { logisticsTimelinessMark } = itemMarkAggregateModel || {};

    if (logisticsTimelinessMark) {
      const timelinessTemplateId = logisticsTimelinessMark?.timelinessTemplateId || 0;
      return {
        isTimelinessTemplateEnable: !!timelinessTemplateId,
        timelinessTemplateId
      };
    }
    return {
      isTimelinessTemplateEnable: false,
      timelinessTemplateId: 0
    };
  },
  [SELL_POINT]: SELL_POINT
};

// 编辑和导入公共的更新修改项转换映射
const CommonTransUpdateFields = {
  [ONLINE_DELIVERY_TEMPLATE]: ({
    [ONLINE_DELIVERY_TEMPLATE]: deliveryTemplate,
    deliveryTemplateInfo
  }) => {
    if (isNil(deliveryTemplate)) {
      return undefined;
    }
    const { deliveryTemplateId, expressPayMode } = deliveryTemplate;
    return transformValByRatio(
      {
        ...deliveryTemplate,
        parentTemplateId: getParentId(deliveryTemplateInfo),
        type: getDeliveryType({
          deliveryTemplateId,
          expressPayMode
        })
      },
      {
        postage: -2
      }
    );
  },
  [ONLINE_LOGISTICS_TIMELINESS_TEMPLATE]: ({
    [ONLINE_LOGISTICS_TIMELINESS_TEMPLATE]: timelinessTemplate
  }) => {
    const timelinessTemplateId = timelinessTemplate?.timelinessTemplateId || 0;

    return { isTimelinessTemplateEnable: !!timelinessTemplateId, timelinessTemplateId };
  },
  [ONLINE_CATEGORY]: ({ [ONLINE_CATEGORY]: category }) => {
    if (!category) {
      return null;
    }
    const { id: leafCategoryId, name: leafCategoryName, pathList = [] } = category;
    // 后端返回的path不包含最后一级，前端拼接
    if (isUndefined(leafCategoryId)) {
      return undefined;
    }

    let leafCategoryPath = leafCategoryName;

    if (pathList.length > 0) {
      leafCategoryPath = `${pathList.map(p => p.name).join(' > ')} > ${leafCategoryName}`;
    }

    return {
      leafCategoryId,
      leafCategoryName,
      leafCategoryPath
    };
  },
  [OFFLINE_GOODS_GROUP]: ({ [OFFLINE_GOODS_GROUP]: groupModels }) => {
    if (!Array.isArray(groupModels)) {
      // 之前可能经过 queryOne 转换
      return groupModels;
    }
    return getOfflineGroupValue(groupModels);
  },
  [GOODS_ATTR]: ({ [GOODS_ATTR]: goodsAttr }) => {
    if (!goodsAttr) {
      return null;
    }
    const goodsAttrObj = JSON.parse(goodsAttr);
    const { propOperate, propItemList } = goodsAttrObj;
    if (propItemList && Array.isArray(propItemList) && propItemList.length > 0) {
      const validateGoodsProps = propItemList.filter(
        item => item.textList && Array.isArray(item.textList) && item.textList.length > 0
      );
      if (validateGoodsProps && validateGoodsProps.length > 0) {
        const propList = validateGoodsProps.map(({ propId, propName, textList }) => ({
          propId,
          propName,
          textList: textList.map(({ textId, textName }) => ({
            id: textId,
            textName
          }))
        }));
        return {
          updateWay: propOperate || UpdateType.Add,
          attrSetting: propList
        };
      }
    }
    return null;
  },
  [ONLINE_SCHEDULE_DISPLAY]: ({ [ONLINE_SCHEDULE_DISPLAY]: onlineScheduleDisplay = {} }) => ({
    ...parseScheduleShelfResponse(onlineScheduleDisplay),
    subKdtIds: onlineScheduleDisplay.subKdtIds
  }),
  [OFFLINE_SCHEDULE_DISPLAY]: ({ [OFFLINE_SCHEDULE_DISPLAY]: offlineScheduleDisplay = {} }) => ({
    ...parseScheduleShelfResponse(offlineScheduleDisplay),
    subKdtIds: offlineScheduleDisplay.subKdtIds
  }),
  [MEITUAN_GOODS_GROUP]: ({ [MEITUAN_GOODS_GROUP]: goodsGroup }) => {
    if (!goodsGroup || goodsGroup.length === 0) {
      return null;
    }
    return +get(goodsGroup, '[0].groupId', '');
  },
  [ELEME_GOODS_GROUP]: ({ [ELEME_GOODS_GROUP]: goodsGroup }) => {
    if (!goodsGroup || goodsGroup.length === 0) {
      return null;
    }
    return +get(goodsGroup, '[0].groupId', '');
  },
  [MEITUAN_SHANGOU_GOODS_GROUP]: ({ [MEITUAN_SHANGOU_GOODS_GROUP]: goodsGroup }) => {
    if (!goodsGroup || goodsGroup.length === 0) {
      return [];
    }
    return goodsGroup.map(({ groupId, upperId }) => {
      if (upperId) {
        return [upperId, groupId];
      }
      return [groupId];
    });
  }
};

// 商品queryone信息 => 批量修改信息 字段映射
export const TRANSFORM_GOODS_FIELDS_MAP = {
  [ProductType.Storage]: transformGoodsField({
    [STORAGE_GOODS_CATEGORY]: 'categoryId',
    [STORAGE_GOODS_NAME]: 'name',
    [GOODS_SUPPLIER]: 'defaultVendorId',
    [GOODS_LIFE_CYCLE]: 'cycleId',
    [GOODS_BRAND]: 'brandId',
    relatedBizId: isHqStoreAndNewModel ? 'itemId' : 'spuId',
    bizId: isHqStoreAndNewModel ? 'itemId' : 'spuId',
    [STORAGE_GOODS_CODE]: 'spuCode',
    [STORAGE_GOODS_NO]: 'spuNo',
    [STORAGE_CATEGORY]: ({ leafCategoryId, leafCategoryName, leafCategoryPath }) => {
      return { leafCategoryId, leafCategoryName, leafCategoryPath };
    },
    [SPU_PRODUCTION_TIME]: 'productionTime'
  }),
  [ProductType.Offline]: transformGoodsField({
    bizId: isHqStoreAndNewModel ? 'itemId' : 'spuId',
    relatedBizId: 'itemId',
    [OFFLINE_GOODS_NAME]: 'name',
    [OFFLINE_GOODS_STATUS]: isHqStore
      ? undefined
      : ({ isDisplay }) => {
          return {
            display: isDisplay
          };
        },
    [OFFLINE_MEMBER_DISCOUNT]: ({ joinLevelDiscount }) => {
      return joinLevelDiscount === 1;
    },
    [OFFLINE_GOODS_GROUP]: ({ groupModels }) => getOfflineGroupValue(groupModels),
    [OFFLINE_SCHEDULE_DISPLAY]: ({ scheduleDisplayModel }) =>
      isHqStore
        ? {
            type: ScheduleShelfType.All
          }
        : parseScheduleShelfResponse(scheduleDisplayModel ?? {}),
    [OFFLINE_BUY_LIMIT]: ({ itemSellModel = {} }) => {
      const { quotaCycle, quota } = itemSellModel;
      if (Number.isInteger(quotaCycle) && itemSellModel.quota !== BuyLimitType.NoQuota) {
        return {
          buyLimitType: quotaCycle,
          buyLimitNum: quota
        };
      }
      // 不限购
      return {
        buyLimitType: BuyLimitType.NoQuota
      };
    },
    ...PurchaseRightTransField
  }),
  [OFFLINE_START_SALE_NUM]: ({ itemSellModel = {} }) => {
    return {
      offlineStartSaleNum: itemSellModel.startSaleNum,
      offlineIncrementSaleNum: itemSellModel.incrementSaleNum
    };
  },
  [ProductType.Online]: transformGoodsField({
    bizId: (data = {}) => {
      const { spuId, stocks } = data;
      if (!isNil(spuId)) {
        return spuId;
      }

      /**
       * 网店直接添加规格绑定商品库商品(不是通过绑定商品库多规格商品来创建多规格商品)
       * SPU 维度上没有和商品库商品关联的 spuId,取规格中关联的 id
       * */

      const relatedId = get(stocks, '[0].relatedItemId') || get(stocks, '[1].relatedItemId');
      return relatedId;
    },
    relatedBizId: 'itemId',
    name: 'title',
    [ONLINE_CATEGORY]: ({ leafCategoryId, leafCategoryName, leafCategoryPath }) => {
      return { leafCategoryId, leafCategoryName, leafCategoryPath };
    },
    [ONLINE_GOODS_ORG]: item => {
      const property = 'shopOrgId';
      if (item[property]) {
        return [item[property]];
      }
      return [];
    },
    [ONLINE_BARCODE]: 'barcode',
    [ONLINE_DELIVERY_TEMPLATE]: isHqStore
      ? undefined
      : data => {
          const {
            postage,
            deliveryTemplateId,
            deliveryTemplateInfo,
            itemMarkAggregateModel
          } = data;
          const expressPayMode = itemMarkAggregateModel?.distributionMark?.expressPayMode || 0;

          return transformValByRatio(
            {
              type: getDeliveryType({
                deliveryTemplateId,
                expressPayMode
              }),
              postage,
              deliveryTemplateId,
              parentTemplateId: getParentId(deliveryTemplateInfo),
              expressPayMode
            },
            {
              postage: -2
            }
          );
        },
    [ONLINE_PRODUCTION_TIME]: 'productionTime',
    // 处理连锁D queryone品牌、分类映射
    [ONLINE_BRAND]: 'brandId',
    [ONLINE_CLASSIFICATION]: 'categoryId',
    ...CommonOnlineTransFields
  }),
  [ProductType.Meituan]: transformGoodsField({
    name: 'itemName',
    spuNo: 'channelItemNo',
    [MEITUAN_GOODS_GROUP]: data => {
      return get(data, 'groupIds[0]');
    },
    relatedBizId: isHqStoreAndNewModel ? 'itemId' : 'channelItemId'
  }),
  [ProductType.Eleme]: transformGoodsField({
    name: 'itemName',
    spuNo: 'channelItemNo',
    [ELEME_GOODS_GROUP]: data => {
      return get(data, 'groupIds[0]');
    },
    relatedBizId: isHqStoreAndNewModel ? 'itemId' : 'channelItemId'
  }),
  [ProductType.MeituanShangou]: transformGoodsField({
    name: 'itemName',
    spuNo: 'channelItemNo',
    [MEITUAN_SHANGOU_GOODS_GROUP]: ({
      [MEITUAN_SHANGOU_GOODS_GROUP]: meituanGoodsGroup,
      groupIdPaths = []
    }) => {
      if (meituanGoodsGroup && meituanGoodsGroup.length > 0) {
        return meituanGoodsGroup;
      }
      return groupIdPaths.map(ids => {
        return ids.split(',').map(id => +id);
      });
    },
    relatedBizId: isHqStoreAndNewModel ? 'itemId' : 'channelItemId'
  })
};

// 导入修改项字段默认映射
export const TransformImportFieldsMap = {
  [ProductType.Storage]: transformGoodsField({
    [STORAGE_CATEGORY]: ({ leafCategoryId, leafCategoryName, leafCategoryPath }) => {
      return { leafCategoryId, leafCategoryName, leafCategoryPath };
    },
    [OFFLINE_CHANNEL]: ({ sellChannelSettingVO }) => {
      if (isNil(sellChannelSettingVO)) {
        return undefined;
      }

      const { isPartial, offlineChannelIds } = sellChannelSettingVO;
      const [offlinePartial] = splitSellChannelPartial(isPartial);
      return {
        isPartial: offlinePartial,
        channelIds: offlineChannelIds
      };
    },
    [ONLINE_CHANNEL]: ({ sellChannelSettingVO }) => {
      if (isNil(sellChannelSettingVO)) {
        return undefined;
      }

      const { isPartial, onlineChannelIds } = sellChannelSettingVO;
      const [onlinePartial] = splitSellChannelPartial(isPartial);
      return {
        isPartial: onlinePartial,
        channelIds: onlineChannelIds
      };
    }
  }),
  [ProductType.Offline]: transformGoodsField({}),
  [ProductType.Online]: transformGoodsField({
    [ONLINE_CATEGORY]: ({ categoryNames, leafCategoryId }) => {
      if (!categoryNames) {
        return null;
      }
      const leafCategoryPath = categoryNames.join(' > ');
      return {
        leafCategoryId,
        leafCategoryPath
      };
    },
    [ONLINE_DELIVERY_TEMPLATE]: isHqStore
      ? undefined
      : ({ postage, deliveryTemplateId }) => {
          return {
            postage,
            deliveryTemplateId
          };
        },
    ...CommonOnlineTransFields
  }),
  [ProductType.Meituan]: transformGoodsField({
    channelItemId: 'itemId'
  }),
  [ProductType.Eleme]: transformGoodsField({
    channelItemId: 'itemId'
  }),
  [ProductType.MeituanShangou]: transformGoodsField({
    channelItemId: 'itemId'
  })
};

// 导入文件修改项字段转换
export const transImportFields = transformGoodsField({
  [STORAGE_GOODS_STATUS]: ({ [STORAGE_GOODS_STATUS]: storageGoodsStatus, inventoryType }) => {
    const newValue = {
      ...storageGoodsStatus,
      isMaterial: inventoryType === InventoryType.Material
    };
    if (inventoryType === InventoryType.Material) {
      delete newValue.affectChannelIds;
      delete newValue.display;
    }
    return newValue;
  },
  [STORAGE_CATEGORY]: ({ [STORAGE_CATEGORY]: category }) => {
    if (!category) {
      return null;
    }
    const { id: leafCategoryId, name: leafCategoryName, pathList } = category;
    // 后端返回的path不包含最后一级，前端拼接
    if (isUndefined(leafCategoryId)) {
      return undefined;
    }
    const leafCategoryPath = `${pathList.map(p => p.name).join(' > ')} > ${leafCategoryName}`;
    return {
      leafCategoryId,
      leafCategoryName,
      leafCategoryPath
    };
  },
  [OFFLINE_BUY_LIMIT]: ({ [OFFLINE_BUY_LIMIT]: offlineBuyLimitParam }) => {
    if (!offlineBuyLimitParam) {
      return null;
    }
    const { offlineBuyLimitNum, offlineByLimitType } = offlineBuyLimitParam;
    return {
      buyLimitNum: offlineBuyLimitNum,
      buyLimitType: offlineByLimitType
    };
  },
  [ONLINE_BUY_LIMIT]: ({ [ONLINE_BUY_LIMIT]: onlineBuyLimitParam }) => {
    if (!onlineBuyLimitParam) {
      return null;
    }
    const { onlineBuyLimitNum, onlineByLimitType } = onlineBuyLimitParam;
    return {
      buyLimitNum: onlineBuyLimitNum,
      buyLimitType: onlineByLimitType
    };
  },
  [STORAGE_INPUT_TAX_RATE]: ({ [STORAGE_INPUT_TAX_RATE]: inputTaxRate }) => {
    if (inputTaxRate === undefined) {
      return null;
    }
    return String(inputTaxRate / 1000000);
  },
  [STORAGE_OUTPUT_TAX_RATE]: ({ [STORAGE_OUTPUT_TAX_RATE]: outputTaxRate }) => {
    if (!outputTaxRate === undefined) {
      return null;
    }
    return String(outputTaxRate / 1000000);
  },
  ...CommonTransUpdateFields
});

// 编辑修改项 字段映射
export const transEditFields = transformGoodsField({
  [STORAGE_CATEGORY]: ({
    [STORAGE_CATEGORY]: category,
    fieldsDescMp: { [STORAGE_CATEGORY]: categoryPath }
  }) => {
    if (!category) {
      return null;
    }

    return {
      leafCategoryId: category,
      leafCategoryPath: categoryPath
    };
  },
  [SPU_SELL_CHANNEL]: ({ [SPU_SELL_CHANNEL]: spuSellChannel }) => {
    if (isNil(spuSellChannel)) {
      return undefined;
    }

    const { isPartial, offlineChannelIds, onlineChannelIds } = deepJsonParse(spuSellChannel);
    const [offlinePartial, onlinePartial] = splitSellChannelPartial(isPartial);
    return {
      [OFFLINE_CHANNEL]: {
        isPartial: offlinePartial,
        channelIds: offlineChannelIds
      },
      [ONLINE_CHANNEL]: {
        isPartial: onlinePartial,
        channelIds: onlineChannelIds
      }
    };
  },
  ...CommonTransUpdateFields
});

// 处理正则特殊字符
function escapeRegExp(str) {
  return str.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
}

export const rename = (name, rules) => {
  const {
    rule,
    [RenameFields.Prefix]: prefix,
    [RenameFields.Suffix]: suffix,
    [RenameFields.Replace]: replace,
    [RenameFields.ReplaceBy]: replaceBy
  } = rules;

  if (rule === RenameRules.Add) {
    return `${prefix || ''}${name || ''}${suffix || ''}`;
  }
  if (rule === RenameRules.Replace && replace) {
    return name.replace(new RegExp(escapeRegExp(replace), 'g'), replaceBy || '');
  }

  return name;
};

/**
 * 对于商品弹窗|商品导入增加的商品，处理下销售渠道
 * 增加表单门店|网店渠道需要的字段
 */
export function handleSaleChannelAfterAdd(goodsList, productType) {
  if (ProductType.Storage === productType) {
    return goodsList.map(goods => {
      const { isPartial, offlineChannelIds, onlineChannelIds } = goods.sellChannelSettingVO || {};
      const [offlinePartial, onlinePartial] = splitSellChannelPartial(isPartial) || [];

      return {
        ...goods,
        [OFFLINE_CHANNEL]: {
          channelIds: offlineChannelIds,
          isPartial: offlinePartial
        },
        [ONLINE_CHANNEL]: {
          channelIds: onlineChannelIds,
          isPartial: onlinePartial
        }
      };
    });
  }
  return goodsList;
}

export async function createCalcSaleChannelWhenBatchChange() {
  const totalOfflineChannelIds = await getTotalOfflineChannelIds();
  const totalOnlineChannelIds = await getTotalOnlineChannelIds();
  const channelIdsMp = {
    [GoodsChannel.Offline]: totalOfflineChannelIds,
    [GoodsChannel.Online]: totalOnlineChannelIds
  };

  return function calcSaleChannelWhenBatchChange({
    channel,
    isPartial,
    channelIds = [],
    addChannelIds = [],
    delChannelIds = []
  }) {
    const curChannelIds =
      (isPartial === IsPartial.Total ? channelIdsMp[channel] : channelIds) || [];
    return uniq(difference([...addChannelIds, ...curChannelIds], delChannelIds));
  };
}

export function handleSaleChannelBeforeSubmit({ offlineChannel, onlineChannel }, { type, fields }) {
  const hasPickSellChannel = fields.includes(SPU_SELL_CHANNEL);
  // 不是修改商品库商品或者未勾选商品渠道，直接退出
  if (type !== ProductType.Storage || !hasPickSellChannel) {
    return null;
  }
  const { isPartial: offlinePartial, channelIds: offlineChannelIds } = offlineChannel;
  const { isPartial: onlinePartial, channelIds: onlineChannelIds } = onlineChannel;
  if (!HasOfflineAbility) {
    return {
      isPartial: combineSellChannelPartial(offlinePartial, onlinePartial),
      onlineChannelIds
    };
  }
  return {
    isPartial: combineSellChannelPartial(offlinePartial, onlinePartial),
    offlineChannelIds,
    onlineChannelIds
  };
}

export const categoryTransform = list => {
  return list.map(item => {
    const {
      categoryId: leafCategoryId = '',
      categoryName: leafCategoryName = '',
      categoryChainList = []
    } = item;
    const leafCategoryPath = categoryChainList.join(' > ');
    return { leafCategoryName, leafCategoryId, leafCategoryPath };
  });
};

/**
 * 构建商品分组修改项参数
 * @param {*} field
 * @param {*} item
 */
const buildOnlineGoodsGroup = (field, item) => {
  const { type, groups = [] } = field;
  const { actualTagIds = [] } = item;
  const groupIds = groups.map(group => group.groupId);

  if (type === GroupModifyType.Append) {
    return uniq([].concat(actualTagIds, groupIds));
  }
  if (type === GroupModifyType.Overwrite) {
    return groupIds;
  }
  if (type === GroupModifyType.Delete) {
    return [];
  }
};

/**
 * 构建商品类目修改项参数
 * @param {*} field
 * @returns
 */
const buildOnlineGoodsCategory = field => {
  if (!field) {
    return null;
  }
  const { leafCategory, onlineCategoryOperateType } = field;
  if (onlineCategoryOperateType === FetchCategoryMethod.Handle) {
    const { leafCategoryId, leafCategoryPath } = leafCategory;
    return {
      [ONLINE_CATEGORY]: leafCategoryId,
      [ONLINE_CATEGORY_DESC]: leafCategoryPath
    };
  }
  if (onlineCategoryOperateType === FetchCategoryMethod.Auto) {
    return {
      onlineCategoryOperateType
    };
  }
};

/**
 * 构建售后服务修改项参数
 * @param {*} field
 * @returns
 */
const buildOnlineAfterSale = field => {
  if (!field) {
    return null;
  }
  const { afterSaleOperateType, afterSaleValue } = field;
  return {
    afterSaleOperateType,
    [ONLINE_AFTER_SALE]: afterSaleValue.reduce((acc, afterSale) => {
      if (afterSale === AfterSale.Exchange) {
        acc[afterSale] = 1;
      } else {
        acc[afterSale] = true;
      }
      return acc;
    }, {})
  };
};

/**
 * 构建配送方式修改项参数
 * @param {*} field
 * @returns
 */
const buildOnlineDistribution = field => {
  if (!field) {
    return null;
  }
  return buildDistributionSearchParams(
    field.map(key => ({
      key
    }))
  );
};

const buildOnlineDeliveryTemplate = field => {
  const { postage, ...rest } = field;
  return {
    ...rest,
    postage: times(postage, 100)
  };
};

/**
 * 按条件修改商品，items 转换方法
 * @param {*} params
 * @param {*} allValues
 * @returns
 */
export const transformConditionParams = (params, allValues) => {
  const { items } = params;

  const { fields, type } = allValues;
  const conditionFields = fields.reduce((acc, field) => {
    if (`condition_${field}` in allValues) {
      acc[field] = allValues[`condition_${field}`];
    }
    return acc;
  }, {});

  const modifyField = goodsApiField[type];
  const omitKeys = [
    ONLINE_GOODS_GROUP,
    ONLINE_GOODS_NAME,
    ONLINE_CATEGORY,
    ONLINE_AFTER_SALE,
    ONLINE_DISTRIBUTION,
    ONLINE_DELIVERY_TEMPLATE,
    // 按条件修改提交接口替换字段
    ONLINE_BRAND,
    ONLINE_CLASSIFICATION,
    ONLINE_SCHEDULE_DISPLAY,
    OFFLINE_SCHEDULE_DISPLAY,
    ONLINE_GOODS_INDEX
  ];

  return {
    ...params,
    items: items.map(item => {
      const {
        barcode,
        photoUrl,
        spuNo,
        goodsNo,
        sellChannel,
        relatedBizId,
        title,
        operateType,
        spuId,
        itemSkuModels,
        num
      } = item;
      const goodsNum = conditionFields[ONLINE_GOODS_INDEX] || num;
      // spuId可能为空
      const bizId =
        spuId || get(itemSkuModels, '[0].relatedItemId') || get(itemSkuModels, '[1].relatedItemId');

      return {
        // 历史问题,应该取barcode
        spuNo: barcode,
        photoUrl,
        sellChannel,
        bizName: title,
        operateType,
        relatedBizId,
        // L版按条件改新增字段
        bizId,
        goodsNo: goodsNo || spuNo,
        [modifyField]: {
          ...omit(conditionFields, omitKeys),
          [ONLINE_DELIVERY_TEMPLATE]: conditionFields[ONLINE_DELIVERY_TEMPLATE]
            ? buildOnlineDeliveryTemplate(conditionFields[ONLINE_DELIVERY_TEMPLATE])
            : null,
          [ONLINE_GOODS_GROUP]: conditionFields[ONLINE_GOODS_GROUP]
            ? buildOnlineGoodsGroup(conditionFields[ONLINE_GOODS_GROUP], item)
            : null,
          [ONLINE_GOODS_NAME]: conditionFields[ONLINE_GOODS_NAME]
            ? rename(item.title, conditionFields[ONLINE_GOODS_NAME])
            : null,
          ...buildOnlineGoodsCategory(conditionFields[ONLINE_CATEGORY]),
          ...buildOnlineAfterSale(conditionFields[ONLINE_AFTER_SALE]),
          [ONLINE_DISTRIBUTION]: conditionFields[ONLINE_DISTRIBUTION]
            ? buildOnlineDistribution(conditionFields[ONLINE_DISTRIBUTION])
            : null,
          ...(conditionFields[ONLINE_BRAND] || null),
          ...(conditionFields[ONLINE_CLASSIFICATION] || null),
          [ONLINE_BUY_LIMIT]: conditionFields[ONLINE_BUY_LIMIT]
            ? {
                onlineByLimitType: conditionFields[ONLINE_BUY_LIMIT].buyLimitType,
                onlineBuyLimitNum: conditionFields[ONLINE_BUY_LIMIT].buyLimitNum
              }
            : null
        },
        extraStr: JSON.stringify({
          [ONLINE_SCHEDULE_DISPLAY]:
            fields.includes(ONLINE_SCHEDULE_DISPLAY) && conditionFields[ONLINE_SCHEDULE_DISPLAY]
              ? {
                  ...transformScheduleShelfData(conditionFields[ONLINE_SCHEDULE_DISPLAY] || {}),
                  subKdtIds: conditionFields[ONLINE_SCHEDULE_DISPLAY].subKdtIds || []
                }
              : {},
          goodsNum,
          goodsNumDesc: String(goodsNum)
        })
      };
    })
  };
};

// 商品类型是否支持绑定店内组织
export function isSupportBindOrg(item) {
  return (
    (item.goodsType === OrgSupportGoodType.RealGoods && item.isVirtual === 0) ||
    item.goodsType === OrgSupportGoodType.HaiTao
  );
}

/**
 * ===== 加工时长字段工具方法 =====
 */

/**
 * 判断是否不能编辑商品库加工时长
 */
export function checkIfCanNotEditSPUProductionTime(item) {
  return (
    // 非产成品, 多单位, 称重和医药商品不能修改
    (isNil(item.inventoryType) ? false : item.inventoryType !== InventoryType.Product) ||
    Boolean(item.multiUnitsSpu) ||
    (item.unitSettings?.length === 1 &&
      item.unitSettings[0].measureType === GoodsMeasurement.Weight) ||
    !isSupportProductionTimeCategory(item.leafCategoryId?.leafCategoryId)
  );
}

/** 是否是新模型下的分销商品 */
export function isFenXiao(goodsType) {
  return goodsType === GoodsType.FenXiao && IsUnityModelWithoutFwh;
}

/* 判断是否为海淘商品 */
export function isHaitao(bizMarkCode) {
  return bizMarkCode === goodsBizMarkCodeMap.haitao;
}

/**
 * 判断是否不能编辑网店商品加工时长
 */
export function checkIfCanNotEditOnlineProductionTime(item) {
  // 非产成品, 多单位, 虚拟商品和医药商品不能修改
  return (
    (isNil(item.inventoryType) ? false : item.inventoryType !== InventoryType.Product) ||
    Boolean(item.multiUnitsSpu) ||
    item.isVirtual !== 0 ||
    !isSupportProductionTimeCategory(item.leafCategoryId)
  );
}

/**
 * 处理商品属性数据
 * @param {*} item
 * @returns
 */
export function handleGoodsAttr(item) {
  if (item) {
    const { attrSetting, updateWay } = item;
    if (attrSetting && Array.isArray(attrSetting) && attrSetting.length > 0) {
      const propList = attrSetting.map(({ propId, propName, textList }) => ({
        propId,
        propName,
        textList: textList.map(({ id, textName }) => ({
          textId: id,
          textName
        }))
      }));
      return {
        propOperate: updateWay,
        propItemList: propList
      };
    }
  }
  return {};
}

export const handleExternalGroup = ({
  meituanGoodsGroup,
  elemeGoodsGroup,
  meituanShangouGoodsGroup
}) => {
  if (meituanGoodsGroup) {
    return {
      channel: SalesChannelEnum.MtWmChannelId,
      groupIds: [meituanGoodsGroup]
    };
  }
  if (elemeGoodsGroup) {
    return {
      channel: SalesChannelEnum.ElemeChannelId,
      groupIds: [elemeGoodsGroup]
    };
  }
  if (meituanShangouGoodsGroup && meituanShangouGoodsGroup.length > 0) {
    return {
      channel: SalesChannelEnum.MtShanGouChannelId,
      groupIds: meituanShangouGoodsGroup.map(items => {
        return items.at(-1);
      })
    };
  }
};

/**
 * 是否是有重量的商品
 * @param {*} item
 */
export function hasWeight(item) {
  const {
    isNonSpec,
    itemMeasModel,
    isMultiUnit,
    stocks = [],
    itemSkuMarkAggregateModelList = []
  } = item;
  if (isMultiUnit || !isNonSpec) {
    const [, ...units] = stocks;
    return (
      /** 选择商品场景 */
      units.length === itemMeasModel?.itemMeasList?.length ||
      /** 商品导入场景 */
      itemSkuMarkAggregateModelList?.length === itemMeasModel?.itemMeasList?.length
    );
  }

  return itemMeasModel?.itemWeight > 0;
}

export const BATCH_MODIFY_MESSSAGE_TYPE = {
  // 加载页面完成
  LOAD_FINISH: 'load_finish',
  // 加载商品数据
  LOAD_GOODS: 'load_goods',
  // 提交修改
  COMMIT_MODIFY: 'commit_modify',
  // 展示提交loading
  COMMIT_LOADING: 'commit_loading',
  // 修改状态
  MODIFY_STATUS: 'modify_status',
  // 请求权益卡数据
  LOAD_ACCOUNT_LEVEL: 'load_account_level'
};

// 和商品管理页通信
export const MESSAGE_WITH_GOODS_MANAGE = {
  INIT: () => {
    // 总容器
    const appContainer = document.getElementById('app-container');
    // 侧边栏
    const sidebar = document.getElementById('shared-sidebar');
    // 顶部
    const appHeader = document.getElementById('app-header');
    // 移除侧边栏
    if (sidebar) {
      document.body.removeChild(sidebar);
    }
    // 移除顶部
    if (appHeader) {
      appContainer.removeChild(appHeader);
    }
    appContainer.style.setProperty('margin-left', 0, 'important');
    window.postMessage({
      type: BATCH_MODIFY_MESSSAGE_TYPE.LOAD_FINISH
    });
  }
};

// 是否内嵌在商品管理页
export const IsInlineGoodsManagePage = window.self !== window.top;

const API_TASK_STATUS = {
  Process: 5,
  Success: 10,
  Failed: -3
};

export function pollTask(serialNo) {
  let tryCount = 0;

  return new Promise((resolve, reject) => {
    const checkStatus = async () => {
      // 调用接口 queryLoadProductBatchStatus 获取任务状态
      const data = await queryLoadProductBatchStatus({ serialNo });
      const { dataKey, status } = data;

      // eslint-disable-next-line no-plusplus
      tryCount++;

      if (status === API_TASK_STATUS.Success) {
        // 当状态为10时，将Promise置为resolved
        resolve(dataKey);
      } else if (status === API_TASK_STATUS.Process) {
        // 超过三分钟，就超时报错
        if (tryCount === 180) {
          reject('任务超时失败');
        }
        // 当状态为0、-3或5时，等待一段时间后继续轮询
        setTimeout(checkStatus, 1000);
      } else if (status === API_TASK_STATUS.Failed) {
        reject(new Error('商品请求出错'));
      } else {
        // 其他状态处理，可根据具体需求进行处理或报错
        reject(new Error('任务状态异常'));
      }
    };

    // 开始执行轮询任务
    checkStatus();
  });
}

const TASK_DETAIL_STATUS = {
  Creating: 0,
  ToCheck: 10,
  Finished: 20,
  Refused: 30,
  Checking: 40,
  ToConfirm: 50,
  Confirm: 60,
  Closed: 70,
  Updating: 80,
  Closing: 90
};

// 轮询任务结果
export function pollTaskDetail(bizBillNo) {
  let tryCount = 0;

  return new Promise((resolve, reject) => {
    const checkStatus = async () => {
      // 调用接口 queryBatchOrderDetail 获取任务状态
      const data = await queryBatchOrderDetail(bizBillNo);
      const { orderStatus } = data;

      // eslint-disable-next-line no-plusplus
      tryCount++;

      if (orderStatus === TASK_DETAIL_STATUS.Confirm) {
        // 当状态为60时，将Promise置为resolved
        resolve(data);
      } else {
        // 超过三分钟，就超时报错
        if (tryCount === 180) {
          reject('任务超时失败');
        }
        setTimeout(checkStatus, 1000);
      }
    };

    // 开始执行轮询任务
    checkStatus();
  });
}

// 获取预售配置的扩展字段
export function getPreSaleExtraData(isHaitao) {
  const preSaleMaxSendDays = isHaitao ? HaitaoMaxSendDays : MaxSendDays;
  return {
    preSaleMaxSendDays,
    etdStartDisabled: isHaitao
  };
}

// 获取渠道文本映射
export const getChannelText = channelType => {
  const channel = ChannelSelectData.find(item => item.value === channelType);
  return channel ? channel.text : channelType;
};
