import React, { useEffect, useMemo, useRef, useState, forwardRef, useCallback } from 'react';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import {
  flow,
  pick,
  get,
  isEmpty,
  find,
  includes,
  isNil,
  omit,
  compact,
  uniq,
  flatten,
  intersectionWith
} from 'lodash';
import { Notify, Dialog, Tabs, BlockLoading } from 'zent';

import { batchFetchData, times, minus, track } from '@youzan/retail-utils';
import { CardWrapper, createDialog, ConfirmAction } from '@youzan/retail-components';
import {
  isHqStore,
  isPartnerStore,
  isRetailSingleStore,
  isUnifiedHqStore,
  isWscSingleStore,
  isRetailMinimalistShop
} from '@youzan/utils-shop';
import { FieldArray, createForm } from '@youzan/retail-form';
import { transformScheduleShelfData } from '@youzan/goods-domain-pc-components';
import queryString from 'query-string';

import {
  IsSupportOperateCondition,
  HasMultiChannelProductManageAbility,
  HasOnlineChannelInMulti,
  isOfflineBranchStore,
  isOnlineBranchStore
} from 'common/constants';
import { isFalsy } from 'common/utils';
import { IsRetailMinimalistOrEduV4 } from 'common/constants';
import { useBatch, useLeaveConfirm } from 'common/hooks';
import ErrorItems from 'cpn/error-items';
import withRegisterForm from 'cpn/with-register-form';
import withLoading from 'cpn/with-loading';
import {
  SAME_DELIVERY,
  DELIVERY_TEMPLATE,
  EXPRESS_PAY_MODE
} from 'cpn/field/batch-operate/delivery-template-field/cpn';

import { isHqStoreAndNewModel } from 'common/constants/data-permissions';
import { openResultDialog } from 'cpn/goods-selection/import/result-dialog';
import { SalesChannelEnum } from '@youzan/goods-domain-definitions';
import CategoryIds from './components/category-ids';
import { checkItems } from './validate';
import { transformDetailData, transformFileData, mergeSelectGoods } from '../utils';
import {
  ONLINE_DELIVERY_TEMPLATE,
  ONLINE_CHANNEL,
  ONLINE_GOODS_GROUP,
  OFFLINE_CHANNEL,
  SPU_SELL_CHANNEL,
  OFFLINE_GOODS_GROUP,
  ONLINE_CATEGORY,
  ONLINE_CATEGORY_DESC,
  STORAGE_CATEGORY,
  STORAGE_CATEGORY_DESC,
  SPU_PRODUCTION_TIME,
  ONLINE_PRODUCTION_TIME,
  SCHEDULE_DISPLAY_OFF,
  ONLINE_CLASSIFICATION,
  ONLINE_BRAND,
  ONLINE_GOODS_ORG,
  GOODS_ATTR,
  ONLINE_ORIGIN,
  ONLINE_SCHEDULE_DISPLAY,
  OFFLINE_SCHEDULE_DISPLAY,
  SELL_POINT,
  STORAGE_GOODS_STATUS,
  PURCHASE_RIGHT_REQUEST,
  OFFLINE_BUY_LIMIT,
  OFFLINE_START_SALE_NUM,
  ONLINE_BUY_LIMIT,
  MEITUAN_GOODS_GROUP,
  ELEME_GOODS_GROUP,
  MEITUAN_SHANGOU_GOODS_GROUP,
  ONLINE_GOODS_INDEX,
  ONLINE_STOCK_DEDUCTION_MODE,
  ONLINE_CRUCIAL_LABEL,
  ONLINE_GENERAL_LABEL,
  ONLINE_MESSAGES,
  OFFLINE_CRUCIAL_LABEL,
  OFFLINE_GENERAL_LABEL,
  JD_WM_PACKING_CHARGES,
  JD_WM_GOODS_GROUP,
  STORAGE_INPUT_TAX_RATE,
  STORAGE_OUTPUT_TAX_RATE,
  STORAGE_SHELF_LIFE_SETTING,
  STORAGE_SUB_TITLE,
  MEITUAN_PACKING_CHARGES,
  ELEME_PACKING_CHARGES,
  OFFLINE_PURCHASE_RIGHT_REQUEST
} from './fields';
import {
  BATCH_MODIFY_MESSSAGE_TYPE,
  handleGoodsAttr,
  MESSAGE_WITH_GOODS_MANAGE,
  IsInlineGoodsManagePage,
  pollTask,
  pollTaskDetail,
  handleExternalGroup
} from './utils';
import { useQueryBizDetail } from '../hooks';

import BaseField from './base-field';
import Action from './action';
import Table from './table';
import Footer from './footer';
import ListFilters from './container/list-filters';
import ModifyItems from './components/modify-items';

import {
  GOODS_LABEL,
  goodsApiField,
  goodsTrackIdMap,
  goodsTypeImportFields,
  MaxGoodsAmount,
  modifyTabs,
  ModifyTabs
} from './constant';
import {
  CommitStatus,
  ProductType,
  ProductOrigin,
  ExecuteQueryStatus,
  OperateType,
  OperateStatus,
  productTypeTextMp,
  NewProcessProductType,
  HandleChannelsMap
} from '../constant';
import * as api from '../api';

import {
  TRANSFORM_GOODS_FIELDS_MAP,
  transEditFields,
  transImportFields,
  TransformImportFieldsMap,
  handleSaleChannelAfterAdd,
  handleSaleChannelBeforeSubmit,
  transformConditionParams,
  isSupportBindOrg
} from './utils';
import { useGetFieldList } from './common/hooks';

import style from './index.scss';
import { useLoadData } from './common/loadData';

const { actionMap } = useBatch;
const { openDialog, closeDialog } = Dialog;

const dialogId = 'batch-modify-error-goods';
const FORM_NAME = 'batch_modify_form';

const DEFAULT_FORM_VALUES = {
  fields: [],
  items: [],
  channelFields: {}
};

const SUPPORT_TAB_TYPES = [
  ProductType.Online,
  ProductType.Storage,
  ProductType.Offline,
  ProductType.Meituan,
  ProductType.Eleme,
  ProductType.MeituanShangou
];

const getConditionTabSupport = type => {
  if (isHqStoreAndNewModel) {
    return SUPPORT_TAB_TYPES.includes(type);
  }

  return type === ProductType.Online;
};

export const TaskStatus = {
  // 任务进行中
  Process: 'process',
  // 任务成功
  Success: 'success',
  // 任务部分成功
  PartSuccess: 'partSuccess',
  // 定时任务，批量改价中存在
  Timing: 'timing'
};

const specialModifyFields = [
  {
    fieldKey: ONLINE_STOCK_DEDUCTION_MODE,
    fieldName: '库存扣减方式',
    handle(value) {
      return String(value);
    }
  },
  {
    fieldKey: ONLINE_CRUCIAL_LABEL,
    fieldName: '商品关键标签',
    handle({ key, labelGroupId }) {
      return JSON.stringify({
        labelGroupId,
        labelList: [key],
        labelGroupType: GOODS_LABEL.CRUCIAL
      });
    }
  },
  {
    fieldKey: ONLINE_GENERAL_LABEL,
    fieldName: '商品普通标签',
    handle(value) {
      return JSON.stringify(
        value.map(({ key, labelGroupId }) => ({
          labelGroupId,
          labelList: [key],
          labelGroupType: GOODS_LABEL.GENERAL
        }))
      );
    }
  },
  {
    fieldKey: ONLINE_MESSAGES,
    fieldName: '商品留言',
    handle(value) {
      return JSON.stringify(
        value.map(({ multiple, required, title, type, includeDate }) => ({
          name: title,
          datetime: +includeDate || 0,
          editable: true,
          type,
          required: +Boolean(required),
          multiple: +Boolean(multiple)
        }))
      );
    }
  },
  {
    fieldKey: OFFLINE_CRUCIAL_LABEL,
    fieldName: '商品关键标签',
    handle({ key, labelGroupId }) {
      return JSON.stringify({
        labelGroupId,
        labelList: [key],
        labelGroupType: GOODS_LABEL.CRUCIAL
      });
    }
  },
  {
    fieldKey: OFFLINE_GENERAL_LABEL,
    fieldName: '商品普通标签',
    handle(value) {
      return JSON.stringify(
        value.map(({ key, labelGroupId }) => ({
          labelGroupId,
          labelList: [key],
          labelGroupType: GOODS_LABEL.GENERAL
        }))
      );
    }
  },
  {
    fieldKey: JD_WM_PACKING_CHARGES,
    fieldName: '打包费',
    handle(value) {
      return JSON.stringify(value);
    }
  },
  {
    fieldKey: JD_WM_GOODS_GROUP,
    fieldName: '商品分组'
  },
  {
    fieldKey: MEITUAN_GOODS_GROUP,
    fieldName: '商品分组',
    handle(value) {
      return JSON.stringify({
        channel: SalesChannelEnum.MtWmChannelId,
        groupIds: [value]
      });
    }
  },
  {
    fieldKey: ELEME_GOODS_GROUP,
    fieldName: '商品分组',
    handle(value) {
      return JSON.stringify({
        channel: SalesChannelEnum.ElemeChannelId,
        groupIds: [value]
      });
    }
  },
  {
    fieldKey: MEITUAN_SHANGOU_GOODS_GROUP,
    fieldName: '商品分组',
    handle(value) {
      return JSON.stringify({
        channel: SalesChannelEnum.MtShanGouChannelId,
        groupIds: value.map(items => {
          return items.at(-1);
        })
      });
    }
  },
  {
    fieldKey: STORAGE_INPUT_TAX_RATE,
    fieldName: '进项税率',
    handle(value) {
      return String(value * 1000000);
    }
  },
  {
    fieldKey: STORAGE_OUTPUT_TAX_RATE,
    fieldName: '销项税率',
    handle(value) {
      return String(value * 1000000);
    }
  },
  {
    fieldKey: STORAGE_SHELF_LIFE_SETTING,
    fieldName: '保质期管理',
    handle(value) {
      return JSON.stringify(value);
    }
  },
  {
    fieldKey: STORAGE_SUB_TITLE,
    fieldName: '分享描述'
  },
  {
    fieldKey: SELL_POINT,
    fieldName: '商品卖点'
  },
  {
    fieldKey: MEITUAN_PACKING_CHARGES,
    fieldName: '打包费',
    handle(value) {
      return JSON.stringify({
        packingCharges: String(value),
        channel: SalesChannelEnum.MtWmChannelId
      });
    }
  },
  {
    fieldKey: ELEME_PACKING_CHARGES,
    fieldName: '打包费',
    handle(value) {
      return JSON.stringify({
        packingCharges: String(value),
        channel: SalesChannelEnum.ElemeChannelId
      });
    }
  },
  {
    fieldKey: OFFLINE_PURCHASE_RIGHT_REQUEST,
    fieldName: '身份限购',
    handle(value) {
      return JSON.stringify(value);
    }
  }
];

export const getProductType = type => {
  if (isHqStoreAndNewModel) {
    return type === ProductType.Storage
      ? NewProcessProductType.Storage
      : NewProcessProductType.Channel;
  }
  return type;
};

const BatchModify = forwardRef((props, ref) => {
  const {
    match = { params: {} },
    location,
    history,
    allValues = {},
    showLoading,
    hideLoading,
    batchArrayUnshift,
    handleSubmit,
    change,
    initialize,
    untouch,
    batchChange,
    // 外部传入的成功回调
    onExternalSuccess,
    // 从商品库传入的allValue数据
    externalAllValues,
    // 从商品库传入的其他数据
    externalOtherValues,
    // 是否来自商品库
    isFromStorage = false,
    // 加载商品参数
    loadDataParams
  } = props;

  const { type: externalType, channel: externalChannel } = externalAllValues || {};
  const { defaultActiveChannel } = externalOtherValues || {};
  const { id: bizBillNo } = match.params;

  const {
    type: internalType,
    items = [],
    fields = [],
    origin,
    isCategoryFetching = false,
    channel,
    channelFields = {}
  } = allValues || {};

  console.log('items', items);

  const hasChangeFieldsRef = useRef(false);
  const propsType = hasChangeFieldsRef.current ? internalType : externalType || internalType;
  const isEdit = !!bizBillNo;
  const startTime = useRef(null);
  const [ifSubmit, setSubmitStatus] = useState(false);
  const [tabId, setTabId] = useState(ModifyTabs.Goods);
  const [filterData, setFilterData] = useState({});
  const [isFetchGoods, setIsFetchGoods] = useState(false);
  const [loadingData, setLoadingData] = useState(false);

  const summary = useQueryBizDetail(bizBillNo);
  const type = useMemo(() => {
    if (HasMultiChannelProductManageAbility) {
      return propsType === ProductType.Storage ? propsType : channel || externalChannel;
    }
    return propsType;
  }, [channel, externalChannel, propsType]);
  const allFields = useGetFieldList(type, origin, tabId);

  let category = ONLINE_CATEGORY;
  let categoryDesc = ONLINE_CATEGORY_DESC;

  const productionKey = type === ProductType.Storage ? SPU_PRODUCTION_TIME : ONLINE_PRODUCTION_TIME;

  if (type === ProductType.Storage) {
    category = STORAGE_CATEGORY;
    categoryDesc = STORAGE_CATEGORY_DESC;
  }

  // 类目资质弹窗提示
  const hasBatchCategory = includes(fields, category);

  // 离开前确认
  useLeaveConfirm({ canLeave: ifSubmit || items.length === 0, history });

  useLoadData({
    loadDataUrl: '/youzan.item.product.import.task/1.0.0',
    loadDataParams,
    isFromStorage,
    batchKeysUrl: '/youzan.retail.product.batch.query.importdatakeys/1.0.0',
    batchDataUrl: '/youzan.retail.product.batch.query.importdata/1.0.0',
    onSetLoading: loading => {
      if (loading) {
        showLoading();
      } else {
        hideLoading();
      }
    },
    onImportSuccess(successList) {
      batchArrayUnshift('items', successList);
    },
    onShowResult(failNum, failDetailsUrl, successNum) {
      if (failNum > 0) {
        openResultDialog({ successNum, failNum, failDetailsUrl });
      }
    }
  });

  const initModifyGroupJump = () => {
    if (!location) return;
    const { field } = queryString.parse(location.search);
    if (field === ONLINE_GOODS_GROUP) {
      // 零售单店/L版总部：商品类型需要选中网店商品
      if (isRetailSingleStore || isUnifiedHqStore) {
        change('type', ProductType.Online);
      }

      change('fields', [ONLINE_GOODS_GROUP]);
    }
  };

  const getFormInitialValues = () => {
    if (HasMultiChannelProductManageAbility) {
      /**  合伙人只能改渠道 */
      const type = isHqStore ? ProductType.Storage : ProductType.Channel;
      const channel = HasOnlineChannelInMulti ? ProductType.Online : ProductType.Offline;
      return {
        type,
        channel
      };
    }
    if (isOfflineBranchStore || isPartnerStore) {
      return {
        type: ProductType.Offline
      };
    }
    if (isOnlineBranchStore || IsRetailMinimalistOrEduV4 || isWscSingleStore) {
      return {
        type: ProductType.Online
      };
    }
    return {
      type: ProductType.Storage
    };
  };

  // 初始化商品类型&来源
  useEffect(() => {
    if (!bizBillNo) {
      const { type, channel } = getFormInitialValues();
      change('type', type);

      if (!isNil(channel)) {
        change('channel', channel);
      }
    }

    change('origin', ProductOrigin.All);

    if (externalAllValues) {
      const { fields, type, channelFields, channel } = externalAllValues || {};

      change('type', type);
      change('fields', fields);

      channel && change('channel', channel);
      channelFields && change('channelFields', channelFields);

      Object.keys(externalAllValues).forEach(key => {
        if (key.includes('condition')) {
          change(key, externalAllValues[key]);
        }
      });
    }

    if (externalOtherValues) {
      const { tabId } = externalOtherValues || {};

      if (tabId) {
        setTabId(tabId);
      }
    }

    initModifyGroupJump();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 重新选择商品类型,按条件改修正
  useEffect(() => {
    if (tabId === ModifyTabs.Conditions && !getConditionTabSupport(type)) {
      setTabId(ModifyTabs.Goods);
    }
  }, [tabId, type]);

  if (!type && !isEmpty(summary)) {
    const { productType, remark, seletedModifyField, source } = summary;
    batchChange({
      type: productType,
      fields: seletedModifyField,
      remark,
      source
    });
  }

  const { start, stop } = batchFetchData({
    url: '/youzan.retail.product.batch.modify.query.execute/1.0.0/detail',
    pageSize: 100,
    handleParams: params => {
      return {
        ...params,
        queryStatus: ExecuteQueryStatus.UpdateDetail,
        bizBillNo
      };
    },
    handleData: data => {
      return data;
    }
  });

  const transformGoods = goods => {
    const transform = TRANSFORM_GOODS_FIELDS_MAP[type];
    return transform(goods);
  };

  const showErrorItems = (errorItems, returnError = false) => {
    hideLoading();
    openDialog({
      title: '商品错误信息',
      dialogId,
      children: (
        <ErrorItems
          close={() => {
            closeDialog(dialogId);
          }}
          uniqKey="relatedBizId"
          initItems={errorItems}
          initTotalItems={items}
          handleConfirm={values => {
            change('items', values);
          }}
          disableOperate={tabId === ModifyTabs.Conditions}
          component={props => {
            return (
              <Table
                allValues={allValues}
                isErrorItems
                {...props}
                returnError={returnError}
                disableOperate={tabId === ModifyTabs.Conditions}
              />
            );
          }}
          checkItems={items => {
            return checkItems(items, fields);
          }}
        />
      )
    });
  };

  const onSuccess = async res => {
    setSubmitStatus(true);

    if (IsInlineGoodsManagePage) {
      const detailData = await pollTaskDetail(res.bizBillNo);
      const { failNum, successNum } = detailData;

      window.postMessage({
        type: BATCH_MODIFY_MESSSAGE_TYPE.MODIFY_STATUS,
        // 存在失败，则修改「部分失败」的状态，否则是「全部成功」的状态
        status: failNum > 0 ? TaskStatus.PartSuccess : TaskStatus.Success,
        bizBillNo: res.bizBillNo,
        failNum,
        successNum
      });
    }
    if (!isEdit) {
      const duration = minus(Date.now(), startTime.current);
      track({
        et: 'custom',
        ei: goodsTrackIdMap[type],
        en: `新建修改${productTypeTextMp.get(type)}单时长`,
        pt: 'goodsBatchOperate',
        params: {
          duration
        }
      });
    }

    Notify.success('提交批量修改单成功');

    // 调用外部传入的成功回调
    if (onExternalSuccess) {
      onExternalSuccess(res);
    } else if (!IsInlineGoodsManagePage && history) {
      history.push('/');
    }

    if (hideLoading) {
      hideLoading();
    }
  };

  const { status, setStatus, batchSubmit } = useBatch({
    isEdit,
    chunk: 100,
    createTaskCache: tabId === ModifyTabs.Goods,
    createTaskApi: api.createTask,
    submitItemsApi: api.batchSubmit,
    queryStatusApi: api.checkTaskStatus,
    queryErrorsApi: api.queryErrorItems,
    diffListOptions: {
      uniqKey: 'relatedBizId'
    },
    onSubmitSuccess: () => {
      if (IsInlineGoodsManagePage) {
        window.postMessage({
          type: BATCH_MODIFY_MESSSAGE_TYPE.MODIFY_STATUS,
          status: TaskStatus.Process
        });
      }
    },
    handleParams: params => {
      if (tabId === ModifyTabs.Conditions) {
        /** fix: type需要转换成多渠道的type */
        return transformConditionParams(params, { ...allValues, type });
      }

      const modifyField = goodsApiField[type];

      const { items } = params;
      console.log('items', items);

      return {
        ...params,
        items: items.map(item => {
          const {
            photoUrl,
            spuNo,
            goodsNo,
            sellChannel,
            bizId,
            relatedBizId,
            name,
            operateType,
            [ONLINE_DELIVERY_TEMPLATE]: onlineDeliveryTemplate,
            [OFFLINE_GOODS_GROUP]: offlineGoodsGroup,
            [category]: onlineCategory,
            [ONLINE_GOODS_GROUP]: onlineGoodsGroup,
            [productionKey]: productionTime,
            [SCHEDULE_DISPLAY_OFF]: scheduleDisplayOff,
            [ONLINE_SCHEDULE_DISPLAY]: onlineScheduleDisplay,
            [OFFLINE_SCHEDULE_DISPLAY]: offlineScheduleDisplay,
            [PURCHASE_RIGHT_REQUEST]: purchaseRightRequest,
            goodsNum,
            onlineClassification,
            onlineBrand,
            onlineShopOrg = [],
            onlineOrigin,
            [GOODS_ATTR]: goodsAttr,
            shopItemGoodsStatus,
            offlineBuyLimitParam,
            onlineBuyLimitParam,
            offlineStartSaleNumParam,
            [MEITUAN_GOODS_GROUP]: meituanGoodsGroup,
            [ELEME_GOODS_GROUP]: elemeGoodsGroup,
            [MEITUAN_SHANGOU_GOODS_GROUP]: meituanShangouGoodsGroup
          } = item;

          // reduex-form内部处理：如果初始值为underfined后面是空字符串直接删除该字段
          // 勾选了划线价同时当前item划线价为undefined说明被清空了
          // 兼容处理了下空字符串
          if (fields.includes(ONLINE_ORIGIN) && onlineOrigin === undefined) {
            item.onlineOrigin = '';
          }
          const classification =
            typeof onlineClassification === 'number'
              ? { classificationId: onlineClassification }
              : onlineClassification;
          const brand = typeof onlineBrand === 'number' ? { brandId: onlineBrand } : onlineBrand;
          const { postage, type: deliveryType, deliveryTemplateId } = onlineDeliveryTemplate || {};
          const saleChannel = handleSaleChannelBeforeSubmit(
            pick(item, OFFLINE_CHANNEL, ONLINE_CHANNEL),
            {
              type,
              fields
            }
          );
          const batchCategoryParams = hasBatchCategory
            ? {
                [category]: onlineCategory?.leafCategoryId || null,
                [categoryDesc]: onlineCategory?.leafCategoryPath || null
              }
            : {};

          // productionTime -1 为删除
          const productionTimestamp = productionTime || -1;

          // 店内组织数据 只取叶子节点
          const idx = onlineShopOrg.length - 1;
          const orgInfo = isSupportBindOrg(item)
            ? {
                onlineShopOrgId: onlineShopOrg[idx]
              }
            : {};

          // 商品属性
          const goodsProps = handleGoodsAttr(goodsAttr);

          const externalGroup = isHqStoreAndNewModel
            ? {}
            : handleExternalGroup({
                meituanGoodsGroup,
                elemeGoodsGroup,
                meituanShangouGoodsGroup
              });

          // 多规格商品不回传规格型号
          if (item?.isNonSpec === false) {
            item.specifications = '';
          }
          console.log('ONLINE_GOODS_GROUP', ONLINE_GOODS_GROUP);

          console.log('specialModifyFields', specialModifyFields);

          const modifyFields = [];
          specialModifyFields
            .filter(field => fields.includes(field.fieldKey))
            .forEach(field => {
              console.log('field', field);
              console.log('item[field.fieldKey]', item[field.fieldKey]);

              if (typeof item[field.fieldKey] !== 'undefined') {
                modifyFields.push({
                  ...field,
                  fieldValue:
                    typeof field.handle === 'function'
                      ? field.handle(item[field.fieldKey])
                      : item[field.fieldKey]
                });
              }
            });

          return {
            spuNo,
            goodsNo,
            photoUrl,
            sellChannel,
            bizId,
            bizName: name,
            operateType,
            [productionKey]: productionTimestamp,
            modifyFields,
            [modifyField]: {
              ...pick(
                item,
                /**
                 *  过滤门店和网店渠道字段，会在 saleChannel 中传递
                 *  后端要求字段改名，onlineClassification打平 classificationId，classificationDesc，onlineBrand 打平brandId brandDesc
                 * */
                fields.filter(
                  field =>
                    ![
                      OFFLINE_CHANNEL,
                      ONLINE_CHANNEL,
                      ONLINE_CLASSIFICATION,
                      ONLINE_BRAND,
                      ONLINE_GOODS_ORG,
                      GOODS_ATTR,
                      ONLINE_SCHEDULE_DISPLAY,
                      OFFLINE_SCHEDULE_DISPLAY,
                      // 后端说有赞网关里面已经添加不了字段，所以放在下边扩展字段中
                      SELL_POINT,
                      STORAGE_GOODS_STATUS,
                      PURCHASE_RIGHT_REQUEST,
                      OFFLINE_BUY_LIMIT,
                      ONLINE_BUY_LIMIT,
                      ONLINE_GOODS_INDEX,
                      MEITUAN_GOODS_GROUP,
                      ELEME_GOODS_GROUP,
                      MEITUAN_SHANGOU_GOODS_GROUP,
                      ...specialModifyFields.map(f => f.fieldKey)
                    ].includes(field)
                )
              ),
              ...saleChannel,
              ...classification,
              ...brand,
              ...orgInfo,
              [OFFLINE_GOODS_GROUP]: offlineGoodsGroup?.ids, // 取 selectedIds
              [ONLINE_DELIVERY_TEMPLATE]: onlineDeliveryTemplate
                ? {
                    ...onlineDeliveryTemplate,
                    deliveryTemplateId:
                      deliveryType === DELIVERY_TEMPLATE ? deliveryTemplateId : null,
                    expressPayMode: +(deliveryType === EXPRESS_PAY_MODE) || null,
                    postage:
                      isFalsy(postage) || deliveryType !== SAME_DELIVERY
                        ? null
                        : times(postage, 100)
                  }
                : null,
              [ONLINE_GOODS_GROUP]: onlineGoodsGroup?.map(v => v.groupId),
              ...batchCategoryParams,
              [productionKey]: productionTimestamp,
              // 定时下架 保持现状
              scheduleDisplayOff:
                includes(fields, SCHEDULE_DISPLAY_OFF) &&
                scheduleDisplayOff?.enableScheduleDisplayOff !== undefined
                  ? scheduleDisplayOff
                  : undefined,
              ...goodsProps,
              [ONLINE_BUY_LIMIT]: fields.includes(ONLINE_BUY_LIMIT)
                ? mapKeysToCamelCase({
                    onlineByLimitType: onlineBuyLimitParam.buyLimitType,
                    onlineBuyLimitNum: onlineBuyLimitParam.buyLimitNum
                  })
                : {},
              ...externalGroup
            },
            relatedBizId,
            extraStr: JSON.stringify({
              [ONLINE_SCHEDULE_DISPLAY]:
                fields.includes(ONLINE_SCHEDULE_DISPLAY) && onlineScheduleDisplay
                  ? {
                      ...transformScheduleShelfData(onlineScheduleDisplay || {}),
                      subKdtIds: onlineScheduleDisplay.subKdtIds || []
                    }
                  : {},
              [OFFLINE_SCHEDULE_DISPLAY]:
                fields.includes(OFFLINE_SCHEDULE_DISPLAY) && offlineScheduleDisplay
                  ? {
                      ...transformScheduleShelfData(offlineScheduleDisplay || {}),
                      subKdtIds: offlineScheduleDisplay.subKdtIds || []
                    }
                  : {},
              [SELL_POINT]:
                !isHqStoreAndNewModel && fields.includes(SELL_POINT)
                  ? item.sellPoint || ''
                  : undefined,
              [STORAGE_GOODS_STATUS]: fields.includes(STORAGE_GOODS_STATUS)
                ? shopItemGoodsStatus
                : {},
              [PURCHASE_RIGHT_REQUEST]: fields.includes(PURCHASE_RIGHT_REQUEST)
                ? purchaseRightRequest
                : {},
              [OFFLINE_BUY_LIMIT]: fields.includes(OFFLINE_BUY_LIMIT)
                ? mapKeysToCamelCase({
                    offlineByLimitType: offlineBuyLimitParam.buyLimitType,
                    offlineBuyLimitNum: offlineBuyLimitParam.buyLimitNum
                  })
                : {},
              [OFFLINE_START_SALE_NUM]: fields.includes(OFFLINE_START_SALE_NUM)
                ? mapKeysToCamelCase(offlineStartSaleNumParam)
                : {},
              goodsNum,
              goodsNumDesc: String(goodsNum)
            })
          };
        })
      };
    },
    datasets: items,
    onSuccess,
    onGoodsError: errorItems => {
      const data = errorItems.map(item => {
        const { relatedBizId } = item;
        const good = find(items, { relatedBizId });
        return {
          ...item,
          ...good
        };
      });
      showErrorItems(data, true);
    },
    onFail: err => {
      hideLoading();
      Notify.error(err.msg || '创建批量修改单失败');
    },
    onTaskTimeout: () => {
      setSubmitStatus(true);
      Notify.success('数据梳理中，请稍候');
      if (history) {
        history.push('/');
      }
    }
  });

  const openConditionDialog = useCallback(msg => {
    createDialog({
      title: '已选中商品数',
      children: ({ onConfirm }) => (
        <>
          <p className="condition-dialog-text">{msg}</p>
          <ConfirmAction onConfirm={onConfirm} align="right" confirmText="重新筛选" />
        </>
      ),
      width: 560
    });
  }, []);

  /**
   * 1、请求第一页，获取 totalCount
   * 2、根据 totalCount 和 pageSize 切割请求，获取剩余分页数
   * 3、Promise.all 并发请求剩余分页
   */
  const fetchAllGoods = () => {
    const pageSize = 50;
    return new Promise((resolve, reject) => {
      api
        .getGoodsList({
          ...filterData.params,
          pageNo: 1,
          pageSize
        })
        .then(({ items, paginator }) => {
          const { totalCount } = paginator || {};
          const restPages = Math.ceil(totalCount / pageSize, 10) - 1;
          change('items', items);
          const requestQueue = [];
          for (let i = 1; i <= restPages; i++) {
            requestQueue.push(
              api.getGoodsList({
                ...filterData.params,
                pageNo: i + 1,
                pageSize
              })
            );
          }
          Promise.all(requestQueue)
            .then(responseQueue => {
              const restItems = responseQueue.reduce((acc, cur) => {
                acc.push(cur.items);
                return acc;
              }, []);
              resolve([items, ...restItems]);
            })
            .catch(reject);
        })
        .catch(reject);
    });
  };

  const handleConfirm = commitStatus => {
    const length = get(items, 'length', 0);
    if (tabId === ModifyTabs.Conditions) {
      if (!filterData.totalCount) {
        openConditionDialog('已选中商品个数为 0，请重新筛选。');
        return;
      }
      if (filterData.totalCount > MaxGoodsAmount) {
        openConditionDialog(
          `本次已选中 ${filterData.totalCount} 个商品，系统单次最多支持批量改 ${MaxGoodsAmount} 个商品，请重新筛选`
        );
        return;
      }
    }
    if (tabId === ModifyTabs.Goods) {
      if (length === 0 || length > MaxGoodsAmount) {
        Notify.error(
          length === 0
            ? '单据明细不能为空'
            : `商品数量不能超过${MaxGoodsAmount}，请移除部分商品后再次操作`
        );
        return;
      }
      const { invalidItems } = checkItems(items, fields);
      if (invalidItems.length > 0) {
        showErrorItems(invalidItems);
        return;
      }
    }

    const submitData = params => {
      if (!IsInlineGoodsManagePage) {
        showLoading();
      }

      if (tabId === ModifyTabs.Goods) {
        batchSubmit(params);
        return;
      }

      if (tabId === ModifyTabs.Conditions) {
        setIsFetchGoods(true);
        fetchAllGoods()
          .then(allItems => {
            const newItems = flatten(allItems).map(item => ({
              ...item,
              relatedBizId: item.itemId,
              name: item.title
            }));
            batchSubmit(params);
            change('items', newItems);
          })
          .catch(err => {
            Notify.error(err?.msg ?? '获取商品列表出错');
          })
          .finally(() => {
            setIsFetchGoods(false);
          });
      }
    };

    handleSubmit(values => {
      if (IsInlineGoodsManagePage) {
        window.postMessage({
          type: BATCH_MODIFY_MESSSAGE_TYPE.COMMIT_LOADING
        });
      }

      const getHandleChannels = () => {
        if (isHqStoreAndNewModel) {
          return {
            handeChannels: Object.keys(channelFields || {}).map(k =>
              HandleChannelsMap.get(Number(k))
            )
          };
        }
        return {};
      };
      const submit = () => {
        const params = {
          ...pick(values, 'remark'),
          seletedModifyField: fields,
          bizBillNo,
          commitStatus,
          operateStatus: bizBillNo ? OperateStatus.Update : OperateStatus.Create,
          productType: getProductType(type),
          productOrigin: origin,
          operateType: OperateType.UpdateGoods,
          ...getHandleChannels()
        };
        // 点击提交，并且选择修改商品上下架状态时埋点
        if (
          commitStatus === CommitStatus.Submit &&
          (includes(fields, 'offlineGoodsStatus') || includes(fields, 'onlineGoodsStatus'))
        ) {
          track({
            et: 'click',
            ei: 'modify_confirm',
            en: '商品批量操作修改总部/bu 门店/网店商品',
            pt: 'goodsBatchOperate',
            params: {
              offline: +includes(fields, 'offlineGoodsStatus'),
              isHeadquarter: +isHqStore,
              online: +includes(fields, 'onlineGoodsStatus')
            }
          });
        }
        submitData(params);
      };

      const categoryIds = uniq(
        compact(
          items.map(item => {
            return item[category] ? item[category].leafCategoryId : null;
          })
        )
      );
      // 微商城单店/教育单店/D版连锁不校验
      if (isWscSingleStore || isRetailMinimalistShop) {
        submit();
        return;
      }
      if (hasBatchCategory && categoryIds.length > 0) {
        api
          .batchCheckCategory({
            categoryIds: JSON.stringify(categoryIds)
          })
          .then(res => {
            const datasetsId = res.filter(item => !!item.isCredentials).map(v => v.categoryId);

            if (datasetsId.length > 0) {
              const datasets = items.filter(item =>
                includes(datasetsId, +item[category]?.leafCategoryId)
              );

              openDialog({
                title: '提示',
                dialogId,
                children: (
                  <ErrorItems
                    close={() => {
                      closeDialog(dialogId);
                    }}
                    isCategoryCredentials
                    uniqKey="relatedBizId"
                    initItems={datasets}
                    initTotalItems={items}
                    handleConfirm={values => {
                      change('items', values);
                    }}
                    onCustomerSubmit={submit}
                    component={props => {
                      return <Table allValues={allValues} isErrorItems {...props} />;
                    }}
                    checkItems={items => {
                      return checkItems(items, fields);
                    }}
                  />
                )
              });
            } else {
              submit();
            }
          })
          .catch(err => {
            Notify.error(err?.msg ?? '商品类目验证出错');
          });
      } else {
        submit();
      }
    })();
  };

  const autoFillImportedGoods = (items = []) => {
    const queryoneField = goodsTypeImportFields[type];
    return items.map(item => {
      const { [queryoneField]: queryoneValue, deliveryTemplateInfo, bizId, relatedBizId } = item;
      const transformedGoodsInfo = TransformImportFieldsMap[type]({
        ...queryoneValue,
        deliveryTemplateInfo
      });

      return {
        ...item,
        ...transformedGoodsInfo,
        // 导入信息里面已经携带了 bizId, relatedBizId
        bizId,
        relatedBizId,
        [queryoneField]: null
      };
    });
  };

  const initImportFields = (successList = []) => {
    const importList = successList.map(item => {
      let { fields = [] } = item;
      fields = fields.filter(({ fieldValue }) => {
        return !isNil(fieldValue);
      });

      return {
        ...item,
        fields
      };
    });

    if (type === ProductType.Online || type === ProductType.Offline) {
      const importFields = importList.reduce((pre, cur) => pre.concat(cur.allFields), []);
      change(
        'fields',
        intersectionWith(allFields, importFields, ({ disabled, value }, { fieldName }) => {
          return !disabled && value === fieldName;
        }).map(v => v.value)
      );
    }

    return importList;
  };

  const handleImportSuccess = (successList, successNum) => {
    if (!successList || successList.length === 0) {
      return;
    }
    if (successList.length > MaxGoodsAmount) {
      if (successList.length > successNum) {
        Notify.error(
          `部分条码对应多个商品，导致导入了${successList.length}个商品超过${MaxGoodsAmount}的上限，请移除部分商品后再次操作`
        );
        return;
      }

      Notify.error(
        `商品数量不能超过${MaxGoodsAmount}，本次文件导入后商品总数为${successList.length}，请移除部分商品后再次操作`
      );
      return;
    }

    batchArrayUnshift(
      'items',
      flow(
        initImportFields,
        autoFillImportedGoods,
        transformDetailData,
        transformFileData,
        transImportFields
      )(successList)
    );
  };

  const handleGoodsChange = (selected = []) => {
    const selectedGoods = flow(handleSaleChannelAfterAdd, transformGoods)(selected, type);
    const goods = mergeSelectGoods(selectedGoods, items, 'relatedBizId');

    change('items', goods);
  };

  const handleTabsChange = tab => {
    setTabId(tab);
    change('items', []);
    setFilterData({});
    setStatus({
      type: actionMap.reset
    });
  };

  const queryLoadProductData = async (subDateKeys, serialNo) => {
    const promises = subDateKeys.map(subDateKey => {
      return api.queryLoadProductData({
        serialNo,
        subDateKey,
        retailSource: 'WEB-RETAIL-AJAX'
      });
    });

    Promise.all(promises)
      .then(allGoods => {
        const flattenedGoods = flatten(allGoods);
        const goods = flattenedGoods.map(item => ({
          ...item,
          ...item.onlineProductVO,
          bizMarkCode: get(item, 'onlineProductVO.bizMark.code')
        }));
        handleGoodsChange(goods);
      })
      .finally(() => {
        setLoadingData(false);
      });
  };

  // 查询dataKeys
  const queryLoadProductDataKeys = async (dataKey, serialNo) => {
    const dataKeys = await api
      .queryLoadProductDataKeys({
        serialNo,
        dataKey,
        retailSource: 'WEB-RETAIL-AJAX'
      })
      .catch(() => {
        setLoadingData(false);
      });

    queryLoadProductData(dataKeys, serialNo);
  };

  // 轮询任务状态
  const queryLoadProductBatchStatus = async serialNo => {
    pollTask(serialNo)
      .then(dataKey => {
        queryLoadProductDataKeys(dataKey, serialNo);
      })
      .catch(err => {
        setLoadingData(false);
        Notify.error(err);
      });
  };

  // 获取serialNo
  const loadProductBatchAsync = async listQueryParams => {
    setLoadingData(true);
    const serialNo = await api.loadProductBatchAsync(listQueryParams).catch(() => {
      setLoadingData(false);
    });

    queryLoadProductBatchStatus(serialNo);
  };

  useEffect(() => {
    if (IsInlineGoodsManagePage) {
      // 初始化页面操作
      MESSAGE_WITH_GOODS_MANAGE.INIT();
    }
    // eslint-disable-next-line prettier/prettier
  }, []);

  useEffect(() => {
    const handleBatchModifyEvent = async event => {
      const { data } = event;
      const { type } = data;

      // 加载商品数据
      if (type === BATCH_MODIFY_MESSSAGE_TYPE.LOAD_GOODS) {
        const { listQueryParams = {} } = data;

        loadProductBatchAsync(listQueryParams);
        // 提交修改，触发提交流程
      } else if (type === BATCH_MODIFY_MESSSAGE_TYPE.COMMIT_MODIFY) {
        handleConfirm(CommitStatus.Submit);
      }
    };

    if (IsInlineGoodsManagePage) {
      // 监听事件
      window.addEventListener('message', handleBatchModifyEvent);
    }

    return () => {
      window.removeEventListener('message', handleBatchModifyEvent);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [items, type, fields]);

  useEffect(() => {
    if (bizBillNo) {
      showLoading();
      start()
        .then(res => {
          const items = flow(
            transformDetailData,
            transEditFields
          )(res).map(item => {
            if (isNil(item[SPU_SELL_CHANNEL])) {
              return item;
            }
            return {
              ...omit(item, SPU_SELL_CHANNEL),
              ...item[SPU_SELL_CHANNEL]
            };
          });
          // set data
          setStatus({
            type: actionMap.fetched,
            datasets: items
          });
          change('items', items);
          hideLoading();
        })
        .catch(err => {
          Notify.error(err.msg || '查询详情失败');
          hideLoading();
        });
    } else {
      startTime.current = Date.now();
    }

    return () => {
      stop();
      hideLoading();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const { fields: externalFileds } = externalAllValues || {};
    const curFields = fields?.length > 0 || hasChangeFieldsRef.current ? fields : externalFileds;

    hasChangeFieldsRef.current = true;
    change(
      'fields',
      (curFields || []).filter(field => allFields.find(v => v.value === field))
    );
  }, [change, fields, allFields, externalAllValues]);

  useEffect(() => {
    if (Object.keys(channelFields).length === 0) {
      return;
    }
    change(
      'fields',
      // 获取channelFields所有的field
      flatten(Object.values(channelFields || {}))
    );
  }, [change, fields, channelFields]);

  const resetForm = () => {
    batchChange(DEFAULT_FORM_VALUES);
  };

  const { loading } = status;
  const showCondition = IsSupportOperateCondition && getConditionTabSupport(type);

  return (
    <div className={style.modify} ref={ref}>
      <BaseField
        isEdit={isEdit}
        resetForm={resetForm}
        type={type}
        origin={origin}
        fields={fields}
        tabId={tabId}
        change={change}
        allFields={allFields}
        defaultActiveChannel={defaultActiveChannel}
      />
      {hasBatchCategory && tabId === ModifyTabs.Goods && (
        <CardWrapper title="获取类目id">
          <CategoryIds />
        </CardWrapper>
      )}

      <CardWrapper title={showCondition ? null : '商品明细'}>
        {showCondition && (
          <Tabs
            className={style.modifyTabs}
            tabs={modifyTabs}
            activeId={tabId}
            onChange={handleTabsChange}
          />
        )}
        {tabId === ModifyTabs.Goods && (
          <>
            <Action
              fields={fields}
              type={type}
              origin={origin}
              items={items}
              onGoodsChange={handleGoodsChange}
              onImportSuccess={handleImportSuccess}
              disabled={isCategoryFetching}
            />
            <BlockLoading loading={IsInlineGoodsManagePage && loadingData}>
              <FieldArray
                name="items"
                component={Table}
                props={{
                  allValues,
                  change,
                  initialize,
                  untouch,
                  batchChange
                }}
                emptyLabel={`暂无商品，单笔单据最多支持 ${MaxGoodsAmount} 条`}
              />
            </BlockLoading>
          </>
        )}
        {tabId === ModifyTabs.Conditions && (
          <ListFilters origin={origin} onFilter={setFilterData} />
        )}
      </CardWrapper>
      {tabId === ModifyTabs.Conditions && (
        <ModifyItems origin={origin} allValues={allValues} change={change} />
      )}
      {!IsInlineGoodsManagePage && (
        <Footer
          disabled={loading || isCategoryFetching || isFetchGoods}
          onConfirm={handleConfirm}
        />
      )}
    </div>
  );
});

export default flow(
  createForm({
    form: FORM_NAME,
    injectFormValue: 'allValues',
    scrollToFirstError: true
  }),
  withLoading,
  withRegisterForm
)(BatchModify);
