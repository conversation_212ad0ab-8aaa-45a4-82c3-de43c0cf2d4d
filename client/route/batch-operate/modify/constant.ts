import {
  isHqStore,
  isSingleStore,
  isUnifiedOfflineBranchStore,
  isUnifiedOnlineBranchStore,
  checkAbilityExpired,
  ShopAbility,
  isBranchStore
} from '@youzan/utils-shop';
import { channelEnum as SelectorChannel } from '@youzan/biz-goods-selector';
import { global } from '@youzan/retail-utils';
import { transformTextMapToTabs } from '@youzan/zan-hasaki';

import {
  IsRetailMinimalistOrEduV4,
  HasOfflineStoreGoodsManageAbility,
  HasMultiChannelProductManageAbility,
  MultiSalesChannelsIds
} from 'common/constants';

import { SalesChannelEnum } from '@youzan/goods-domain-pc-components';
import {
  ProductType,
  productTypeTextMp,
  ProductOrigin,
  ProductOriginTextMp,
  GoodsType,
  ChannelTextMap
} from '../constant';
import { isHqStoreAndNewModel } from 'common/constants/data-permissions';

const {
  isStoreSync: isOfflineGroupSync,
  selfCreateConfig = {},
  isNewCategory,
  express_delivery: expressDelivery,
  isInformPickUpFoodOn: isInformPickUpFoodOnRaw = false,
  estimated_arrival: logisticsTimeliness = false
} = global.BUSINESS;

const {
  subshop_create_goods: subshopCreateGoods,
  subshop_price_independent: subshopPriceIndependent,
  subshop_delivery_independent: subshopDeliveryIndependent,
  subshop_goods_info_independent: subshopGoodsInfoIndependent = '{}'
} = selfCreateConfig;

export { isOfflineGroupSync };

// 网店是否自建商品
export const isCreateIndependent = +subshopCreateGoods === 1;
// 网店是否自定义价格
export const isPriceIndependent = +subshopPriceIndependent === 1;
// 网店是否自定义运费模版
export const isDeliveryIndependent = +subshopDeliveryIndependent === 1;
// 网店是否自定义商品名称
export const isGoodsTitleIndependent = +JSON.parse(subshopGoodsInfoIndependent)?.title === 1;
// 网店是否自定义商品详情
export const isGoodsDetailIndependent = +JSON.parse(subshopGoodsInfoIndependent)?.detail === 1;

export const MaxGoodsAmount = 5000;

export const isInformPickUpFoodOn = Boolean(isInformPickUpFoodOnRaw);

// 店铺是否新类目
export { isNewCategory };

// 店铺物流行业能力
export const expressDeliveryAbility = +expressDelivery === 1;

// 店铺是否在预计送达时间白名单内
export const isInLogisticsTimelinessWhitelist = logisticsTimeliness;

// 门店商品管理能力是否过期
const OfflineStoreGoodsManageAbilityExpired = checkAbilityExpired(
  ShopAbility.OfflineStoreGoodsManageAbility
);

export const GOODS_TYPE_SELECT_DATA = [
  {
    text: productTypeTextMp.get(ProductType.Storage),
    value: ProductType.Storage,
    // 除连锁总部、单店、极简版外隐藏（无商品库）
    isHide: (!isHqStore && !isSingleStore) || IsRetailMinimalistOrEduV4
  },
  {
    text: productTypeTextMp.get(ProductType.Offline),
    value: ProductType.Offline,
    disabled: OfflineStoreGoodsManageAbilityExpired,
    isHide:
      !(HasOfflineStoreGoodsManageAbility || OfflineStoreGoodsManageAbilityExpired) ||
      isUnifiedOnlineBranchStore ||
      IsRetailMinimalistOrEduV4 ||
      HasMultiChannelProductManageAbility
  },
  {
    text: productTypeTextMp.get(ProductType.Online),
    value: ProductType.Online,
    isHide: isUnifiedOfflineBranchStore || HasMultiChannelProductManageAbility
  },
  {
    text: productTypeTextMp.get(ProductType.Channel),
    value: ProductType.Channel,
    isHide: !HasMultiChannelProductManageAbility
  }
].filter(item => {
  return !item.isHide;
});

export const ChannelSelectData = [
  {
    text: ChannelTextMap.get(ProductType.Online),
    value: ProductType.Online,
    isHide: !MultiSalesChannelsIds.includes(SalesChannelEnum.OnlineChannelId)
  },
  {
    text: ChannelTextMap.get(ProductType.Offline),
    value: ProductType.Offline,
    isHide: !MultiSalesChannelsIds.includes(SalesChannelEnum.OfflineChannelId)
  },
  {
    text: ChannelTextMap.get(ProductType.Meituan),
    value: ProductType.Meituan,
    isHide: !MultiSalesChannelsIds.includes(SalesChannelEnum.MtWmChannelId) || isBranchStore
  },
  {
    text: ChannelTextMap.get(ProductType.Eleme),
    value: ProductType.Eleme,
    isHide: !MultiSalesChannelsIds.includes(SalesChannelEnum.ElemeChannelId) || isBranchStore
  },
  {
    text: ChannelTextMap.get(ProductType.MeituanShangou),
    value: ProductType.MeituanShangou,
    isHide: !MultiSalesChannelsIds.includes(SalesChannelEnum.MtShanGouChannelId) || isBranchStore
  },
  {
    text: ChannelTextMap.get(ProductType.JdWm),
    value: ProductType.JdWm,
    isHide: !MultiSalesChannelsIds.includes(SalesChannelEnum.JdChannelId) || isBranchStore
  }
].filter(item => !item.isHide);

export const PRODUCT_ORIGIN_SELECT_DATA = [
  {
    text: ProductOriginTextMp.get(ProductOrigin.All),
    value: ProductOrigin.All
  },
  {
    text: ProductOriginTextMp.get(ProductOrigin.HqCreate),
    value: ProductOrigin.HqCreate
  },
  {
    text: ProductOriginTextMp.get(ProductOrigin.OnlineCreate),
    value: ProductOrigin.OnlineCreate
  }
];

export const goodsTypeSearchMp = {
  [ProductType.Storage]: {
    spuType: 1,
    needSkuInfo: false
  },
  [ProductType.Offline]: {
    isDisplays: [0, 1]
  },
  [ProductType.Online]: {
    isDisplays: [0, 1]
  },
  [ProductType.Meituan]: {
    isDisplays: [0, 1],
    salesStatus: 2,
    channelList: [200]
  },
  [ProductType.Eleme]: {
    isDisplays: [0, 1],
    salesStatus: 2,
    channelList: [201]
  },
  [ProductType.MeituanShangou]: {
    isDisplays: [0, 1],
    salesStatus: 2,
    channelList: [203]
  }
};

export const goodsChannelMp = {
  [ProductType.Storage]: SelectorChannel.storage,
  [ProductType.Offline]: SelectorChannel.offline,
  [ProductType.Online]: SelectorChannel.online,
  [ProductType.Meituan]: SelectorChannel.external,
  [ProductType.Eleme]: SelectorChannel.external,
  [ProductType.MeituanShangou]: SelectorChannel.external
};

export const goodsApiField = {
  [ProductType.Storage]: 'productBatchSpuRequest',
  [ProductType.Offline]: 'productBatchOfflineRequest',
  [ProductType.Online]: 'productBatchOnlineRequest',
  [ProductType.Meituan]: 'productBatchExternalRequest',
  [ProductType.Eleme]: 'productBatchExternalRequest',
  [ProductType.MeituanShangou]: 'productBatchExternalRequest'
};

export const goodsTrackIdMap = {
  [ProductType.Storage]: 'batch_modify_storage_duration',
  [ProductType.Offline]: 'batch_modify_offline_duration',
  [ProductType.Online]: 'batch_modify_online_duration'
};

export const goodsTypeImportFields = {
  [ProductType.Storage]: 'spuVO',
  [ProductType.Offline]: 'offlineProductVO',
  [ProductType.Online]: 'onlineProductVO',
  [ProductType.Meituan]: 'channelTemplateVO',
  [ProductType.Eleme]: 'channelTemplateVO',
  [ProductType.MeituanShangou]: 'channelTemplateVO'
};

export enum ModifyTabs {
  Goods,
  Conditions
}

export const modifyTabsTextMap = new Map([
  [ModifyTabs.Goods, '按商品修改'],
  [ModifyTabs.Conditions, isHqStoreAndNewModel ? '统一修改' : '按条件修改']
]);

export const modifyTabs = transformTextMapToTabs(modifyTabsTextMap);

export const goodsTypeMap = new Map([
  [GoodsType.NormalGoods, '普通商品'],
  [GoodsType.DiscountGoods, '降价商品'],
  [GoodsType.FoodGoods, '餐饮商品'],
  [GoodsType.FenXiaoGoods, '分销商品'],
  [GoodsType.MemberGoods, '会员卡'],
  [GoodsType.GiftGoods, '礼品卡商品'],
  [GoodsType.YouzanMettingGoods, '有赞会议商品'],
  [GoodsType.CycleBuyGoods, '周期购'],
  [GoodsType.WholesaleGoods, '批发'],
  [GoodsType.VirtualGoods, '虚拟商品'],
  [GoodsType.KnowledgePayGoods, '知识付费商品'],
  [GoodsType.HotelGoods, '酒店类商品'],
  [GoodsType.NormalServiceGoods, '普通服务类商品'],
  [GoodsType.NormalVirtualGoods, '普通虚拟商品'],
  [GoodsType.ECardGoods, '电子卡券商品'],
  [GoodsType.NormalSpecialVirtualGoods, '普通虚拟商品'],
  [GoodsType.ECardSpecialGoods, '电子卡券商品'],
  [GoodsType.OutsideMemberGoods, '外部会员卡商品'],
  [GoodsType.OutsideCashGoods, '外部直接收款商品'],
  [GoodsType.OutsideNormalGoods, '外部普通商品'],
  [GoodsType.MockNotExitGoods, 'mock不存在商品'],
  [GoodsType.AppletsQrcodeGoods, '小程序二维码']
]);

// 商品业务类型代码
export const goodsBizMarkCodeMap = {
  haitao: '010000000043'
};

export enum GOODS_LABEL {
  CRUCIAL = 1,
  GENERAL = 0
}
