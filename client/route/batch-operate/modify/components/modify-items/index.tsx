import * as React from 'react';
import { useCallback } from 'react';
import { CardWrapper } from '@youzan/retail-components';

import { ModifyItemMap } from './constant';
import fieldTextMap from '../../../field-text';
import { FIELD_VALIDATE_MAP } from '../../validate';

import * as style from '../../index.scss';
import { getChannelText } from '../../utils';
import { isHqStoreAndNewModel } from 'common/constants/data-permissions';
import MultiChannelPopSelect, { ISalesValueProps } from 'cpn/multi-channel-shop-sale';
import { SubShopStatus } from 'common/types';
import { Link as SamLink } from '@youzan/sam-components';

interface IProps {
  allValues: {
    fields: string[];
    channelFields: Object
  };
}

const ModifyItems: React.FC<IProps> = ({ allValues }) => {
  console.log('allValues', allValues);
  
  const { fields = [], channelFields = {}, items } = allValues;

  console.log('channelFields', channelFields, fields, items);
  
  const getValidators = useCallback(({ field, validate, label }) => {
    const requiredValidator = {
      type: 'required',
      message: `请选择${label}`
    };
    const fieldValidator = (FIELD_VALIDATE_MAP as any)[field];

    const validators = [];
    if (validate) {
      validators.push(...validate);
    }
    if (fieldValidator) {
      validators.push(...fieldValidator);
    }
    validators.push(requiredValidator);

    return validators;
  }, []);

  const [visible, setVisible] = React.useState(false);

  const renderFields = (fields: string[]) => {
    return fields.map(field => {
      const modifyItem = (ModifyItemMap as any)[field];
      if (modifyItem) {
        const label = (fieldTextMap as any)[field];
        const { component: ModifyItemCpn, validate = [], props: cpnProps } = modifyItem;
        const newValidate = getValidators({
          validate,
          field,
          label
        });
        // eslint-disable-next-line
        // @ts-ignore
        return (
          <ModifyItemCpn
            key={field}
            name={`condition_${field}`}
            label={`${label}：`}
            validate={newValidate}
            allValues={allValues}
            {...cpnProps}
          />
        );
      }
      return null;
    })
  }

  const renderChannelFields = (channelFields = {}) => {
    const channelFieldsKeys = Object.keys(channelFields)
    
    return channelFieldsKeys.map(channelType => {
      const fields = (channelFields as any)[channelType] || [];
      const channelText = `${getChannelText(Number(channelType))}：`;
      return (
        <div>
          <div className={style.modifyItemHeader}>{channelText}</div>
          {renderFields(fields)}
        </div>
      );
    })
  }

  const hasChannelFields = !!Object.keys(channelFields).length;

  const closeDialog = () => {
    setVisible(false);
  };

  const handleConfirm = (val: ISalesValueProps, isAllShop: boolean) => {

    setVisible(false);
  };

  const openDialog = () => {
    setVisible(true);
  };

  const title = (
    <>
      <span>批量修改</span>
      {
        isHqStoreAndNewModel ? (
          <>
            已选店铺：{}
            <SamLink className="open-select__dialog" onClick={openDialog}>
              选择店铺
            </SamLink>
            <MultiChannelPopSelect
              otherProps={{
                title: '选择店铺'
              }}
              visible={visible}
              saleStatus={SubShopStatus.All}
              closeDialog={closeDialog}
              onConfirm={handleConfirm}
              showAction={false}
            />
          </>
        ) : null
      }
    </>
  )
  return (
    <div>
      <CardWrapper title={title}>
        {fields.length === 0 && <div className={style.modifyItemEmpty}>暂未选择修改信息</div>}
        {
          hasChannelFields ? renderChannelFields(channelFields) : renderFields(fields)
        }
      </CardWrapper>
    </div>
  );
};

export default ModifyItems;
