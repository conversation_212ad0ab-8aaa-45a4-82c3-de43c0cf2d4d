import { isNil, compact } from 'lodash';
import { validator } from '@youzan/retail-form/typings';
import { isWscSingleStore } from '@youzan/utils-shop';

import { FetchCategoryMethod, IsUnityModelWithoutFwh } from 'common/constants';

import {
  OnlineGoodsStatusField,
  OnlineGoodsGroup,
  GoodsTemplateSelectField,
  BatchRenameField,
  DiscountSelectField,
  SaleTimeField,
  BuyLimitField,
  DeliveryTemplateField,
  OriginPriceField,
  CategoryParam,
  DistributionField,
  AfterSaleField,
  SaleNumStartField,
  ShowStockField,
  PrepareTimeField,
  SaleTypeField,
  ScheduleDisplayOffField,
  GoodsBrandSelectField,
  GoodsClassificationSelectField,
  HeavyContinuedField,
  ScheduleDisplayField,
  GoodsIndexField,
  StorageGoodsStatusField,
  GoodsCategoryField,
  LifeCycleSelectField,
  DeliveryTypeField,
  InputTaxRateField,
  SalesTaxRateField,
  ShelfLifeWarningField,
  ShareDescField,
  GoodsSpecialLabelField,
  ExternalGroupField,
} from 'cpn/field/batch-operate';
import BarCodeConditionCpn from 'cpn/field/batch-operate/online-barcode-field/condition-cpn';
import GoodsNoConditionCpn from 'cpn/field/batch-operate/online-goods-no/condition-cpn';
import { Rules } from 'cpn/field/batch-operate/batch-rename/constants';
import { GroupModifyType } from 'cpn/field/batch-operate/online-goods-group/constants';
import { BuyLimitType } from 'cpn/field/batch-operate/buy-limit-field/constant';
import { SupplierSelectField } from '@youzan/biz-select-center';
import { Field } from '@youzan/retail-form';

import {
  ONLINE_GOODS_STATUS,
  ONLINE_GOODS_GROUP,
  ONLINE_GOODS_TEMPLATE,
  ONLINE_GOODS_NAME,
  ONLINE_MEMBER_DISCOUNT,
  ONLINE_SALE_TIME,
  ONLINE_BUY_LIMIT,
  ONLINE_DELIVERY_TEMPLATE,
  ONLINE_ORIGIN,
  ONLINE_CATEGORY,
  ONLINE_GOODS_NO,
  ONLINE_DISTRIBUTION,
  ONLINE_AFTER_SALE,
  ONLINE_BARCODE,
  ONLINE_START_SALE_NUM,
  ONLINE_SHOW_STOCK,
  ONLINE_PREPARE_TIME,
  ONLINE_SALE_TYPE,
  SCHEDULE_DISPLAY_OFF,
  ONLINE_CLASSIFICATION,
  ONLINE_BRAND,
  ONLINE_HEAVY_CONTINUED,
  ONLINE_SCHEDULE_DISPLAY,
  ONLINE_GOODS_INDEX,
  STORAGE_GOODS_STATUS,
  STORAGE_GOODS_CATEGORY,
  STORAGE_GOODS_NAME,
  GOODS_SUPPLIER,
  GOODS_BRAND,
  GOODS_LIFE_CYCLE,
  DELIVERY_TYPE,
  STORAGE_GOODS_NO,
  STORAGE_GOODS_CODE,
  STORAGE_SKU_PATTERN,
  STORAGE_INPUT_TAX_RATE,
  STORAGE_OUTPUT_TAX_RATE,
  STORAGE_SHELF_LIFE_SETTING,
  STORAGE_SUB_TITLE,
  SELL_POINT,
  STORAGE_CATEGORY,
  GOODS_ATTR,
  OFFLINE_GOODS_STATUS,
  OFFLINE_GOODS_NAME,
  OFFLINE_MEMBER_DISCOUNT,
  OFFLINE_GOODS_GROUP,
  OFFLINE_SCHEDULE_DISPLAY,
  OFFLINE_BUY_LIMIT,
  OFFLINE_START_SALE_NUM,
  ONLINE_CRUCIAL_LABEL,
  ONLINE_GENERAL_LABEL,
  OFFLINE_CRUCIAL_LABEL,
  OFFLINE_GENERAL_LABEL,
  ONLINE_MESSAGES,
  MEITUAN_GOODS_GROUP,
  ELEME_GOODS_GROUP
} from '../../fields';
import { SELLING_POINT_VALIDATE, STORAGE_GOODS_CODE_VALIDATE, STORAGE_GOODS_NO_VALIDATE, STORAGE_GOODS_STATUS_VALIDATE, STORAGE_INPUT_TAX_RATE_VALIDATE, STORAGE_SALES_TAX_RATE_VALIDATE, STORAGE_SHARE_DESC_VALIDATE, STORAGE_SHELF_LIFE_WARNING_VALIDATE, STORAGE_SKU_PATTERN_VALIDATE } from '../../validate';
import { GoodsBrandSelector } from '@youzan/retail-components';
import { MaxLength } from 'cpn/field/batch-operate/selling-point-field';
import AttrSettingColCpn from 'cpn/field/batch-operate/goods-attr/column-cpn';
import { global } from '@youzan/retail-utils';
import CommentColumn from 'cpn/field/batch-operate/comment-field/column-cpn';
import { SalesChannelEnum } from '@youzan/goods-domain-definitions';

const { GoodsGroupConditionField } = OnlineGoodsGroup;
const { BuyLimitConditionCpn } = BuyLimitField;
const { CategoryConditionCpn } = CategoryParam;
const { DistributionConditionCpn } = DistributionField;
const { AfterSaleConditionCpn } = AfterSaleField;
const { SaleNumStartConditionCpn } = SaleNumStartField;
const { StockShowCondition } = ShowStockField;
const { PrepareTimeBatch } = PrepareTimeField;
const { SaleTypeConditionCpn } = SaleTypeField;
const { RenameConditionCpn } = BatchRenameField;
const { ScheduleDisplayOffConditionCpn } = ScheduleDisplayOffField;
const { GoodsBrandColumnCpn } = GoodsBrandSelectField;
const { GoodsClassificationColumnCpn } = GoodsClassificationSelectField;
const { GoodsIndexConditionCpn } = GoodsIndexField;
const { Field: GoodsBrandSelectorField } = GoodsBrandSelector;
const { BUSINESS } = global;

export const ModifyItemMap: Record<
  string,
  {
    component: React.ReactNode;
    validate?: {
      type: string;
      rule?: typeof validator | undefined;
      message?: string;
    }[];
    props?: any;
  }
  > = {
  [STORAGE_GOODS_STATUS]: {
    component: StorageGoodsStatusField,
    validate: STORAGE_GOODS_STATUS_VALIDATE
  },
  [STORAGE_GOODS_CATEGORY]: {
    component: GoodsCategoryField
  },
  [STORAGE_GOODS_NAME]: {
    component: RenameConditionCpn,
    validate: [
      {
        type: 'validator',
        rule: (value: any = {}) => {
          const { rule, prefix, suffix, replace } = value;
          if (isNil(rule)) {
            return '请选择名称修改方式及内容';
          }
          if (rule === Rules.Add && !prefix && !suffix) {
            return '前缀与后缀不能同时为空';
          }
          if (rule === Rules.Replace && !replace) {
            return '原有文字不能为空';
          }
          return void 0;
        }
      }
    ]
  },
  [GOODS_SUPPLIER]: {
    component: ((props: any) => {
      return <SupplierSelectField
        label={null}
        name={GOODS_SUPPLIER}
        props={{ showTotal: false, isNeedCycleFind: true }}
        {...props}
      />
    })
  },
  [GOODS_BRAND]: {
    component: (props: any) => {
      return <GoodsBrandSelectorField name={GOODS_BRAND} type="RF" {...props} />
    }
  },
  [GOODS_LIFE_CYCLE]: {
    component: LifeCycleSelectField,
    props: {
      label: '生命周期'
    }
  },
  [DELIVERY_TYPE]: {
    component: DeliveryTypeField,
    props: {
      label: '物流模式'
    }
  },
  [STORAGE_GOODS_NO]: {
    component: ((props: any) => {
      return <Field
        name={STORAGE_GOODS_NO}
        component="InputField"
        placeholder="请输入"
        {...props}
      />
    }),
    props: {
      label: '商品条码：',
      placeholder: '请输入字母或数字'
    },
    validate: STORAGE_GOODS_NO_VALIDATE
  },
  [STORAGE_GOODS_CODE]: {
    component: ((props: any) => {
      return <Field
        name={STORAGE_GOODS_CODE}
        component="InputField"
        placeholder="请输入"
        {...props}
      />
    }),
    props: {
      label: '商品编码：',
      placeholder: '请输入'
    },
    validate: STORAGE_GOODS_CODE_VALIDATE
  },
  [STORAGE_SKU_PATTERN]: {
    component: ((props: any) => {
      return <Field
        name={STORAGE_SKU_PATTERN}
        component="InputField"
        placeholder="请输入"
        {...props}
      />
    }),
    props: {
      label: '规格型号：',
      placeholder: '请输入'
    },
    validate: STORAGE_SKU_PATTERN_VALIDATE as any
  },
  [STORAGE_INPUT_TAX_RATE]: {
    component: InputTaxRateField,
    props: {
      label: '进项税率：',
    },
    validate: STORAGE_INPUT_TAX_RATE_VALIDATE
  },
  [STORAGE_OUTPUT_TAX_RATE]: {
    component: SalesTaxRateField,
    props: {
      label: '销项税率：',
    },
    validate: STORAGE_SALES_TAX_RATE_VALIDATE
  },
  [STORAGE_SHELF_LIFE_SETTING]: {
    component: ShelfLifeWarningField,
    props: {
      label: '保质期管理：',
    },
    validate: STORAGE_SHELF_LIFE_WARNING_VALIDATE
  },
  [STORAGE_SUB_TITLE]: {
    component: ShareDescField,
    props: {
      label: '分享描述：',
      name: STORAGE_SUB_TITLE
    },
    validate: STORAGE_SHARE_DESC_VALIDATE as any
  },
  [SELL_POINT]: {
    component: (props: any) => {
      return (
        <Field
          name={SELL_POINT}
          component="InputField"
          props={{ type: 'textarea', maxlength: MaxLength, placeholder: '请输入商品卖点' }}
          {...props}
        />
      )
    },
    props: {
      label: '商品卖点：',
      placeholder: '请输入'
    },
    validate: SELLING_POINT_VALIDATE
  },
  [STORAGE_CATEGORY]: {
    component: CategoryConditionCpn,
    validate: [
      {
        type: 'validator',
        rule: (value: any = {}) => {
          const { onlineCategoryOperateType, leafCategory } = value;
          if (isNil(onlineCategoryOperateType)) {
            return '请选择商品类目';
          }
          if (onlineCategoryOperateType === FetchCategoryMethod.Handle && isNil(leafCategory)) {
            return '请选择手动修改的商品类目';
          }
          return void 0;
        }
      }
    ]
  },
  [GOODS_ATTR]: {
    component: (props: any) => {
      return (
        <AttrSettingColCpn
          showTip={BUSINESS.skuAttrSwitch}
          {...props}
        />
      )
    }
  },
  [OFFLINE_GOODS_STATUS]: {
    component: OnlineGoodsStatusField
  },
  [OFFLINE_GOODS_NAME]: {
    component: RenameConditionCpn,
    validate: [
      {
        type: 'validator',
        rule: (value: any = {}) => {
          const { rule, prefix, suffix, replace } = value;
          if (isNil(rule)) {
            return '请选择名称修改方式及内容';
          }
          if (rule === Rules.Add && !prefix && !suffix) {
            return '前缀与后缀不能同时为空';
          }
          if (rule === Rules.Replace && !replace) {
            return '原有文字不能为空';
          }
          return void 0;
        }
      }
    ]
  },
  [OFFLINE_MEMBER_DISCOUNT]: {
    component: DiscountSelectField,
    props: {
      width: 195
    }
  },
  [OFFLINE_GOODS_GROUP]: {
    component: GoodsGroupConditionField,
    validate: [
      {
        type: 'validator',
        rule: (value: any = {}) => {
          const { type, groups = [] } = value;
          if (isNil(type)) {
            return '请选择商品分组修改方式及分组';
          }
          if (
            (type === GroupModifyType.Append || type === GroupModifyType.Overwrite) &&
            groups.length === 0
          ) {
            return '请选择商品分组修改方式及分组';
          }
          return void 0;
        }
      }
    ]
  },
  [OFFLINE_SCHEDULE_DISPLAY]: {
    component: ScheduleDisplayField,
    props: {
      targetName: OFFLINE_SCHEDULE_DISPLAY
    }
  },
  [OFFLINE_BUY_LIMIT]: {
    component: BuyLimitConditionCpn,
    props: {
      buyLimitTypes: isWscSingleStore
        ? undefined
        : compact([
            IsUnityModelWithoutFwh ? BuyLimitType.OrderQuota : null,
            BuyLimitType.Always,
            BuyLimitType.PeriodQuota
          ])
    }
  },
  [OFFLINE_START_SALE_NUM]: {
    component: SaleNumStartConditionCpn
  },
  [OFFLINE_CRUCIAL_LABEL]: {
    component: GoodsSpecialLabelField,
    props: {
      label: '商品关键标签：'
    },
    validate: [
      { type: 'required', message: '请选择商品关键标签' }
    ]
  },
  [OFFLINE_GENERAL_LABEL]: {
    component: GoodsSpecialLabelField,
    props: {
      label: '商品普通标签：'
    },
    validate: [
      { type: 'required', message: '请选择商品普通标签' }
    ]
  },
  [ONLINE_GOODS_STATUS]: {
    component: OnlineGoodsStatusField
  },
  [ONLINE_GOODS_GROUP]: {
    component: GoodsGroupConditionField,
    validate: [
      {
        type: 'validator',
        rule: (value: any = {}) => {
          const { type, groups = [] } = value;
          if (isNil(type)) {
            return '请选择商品分组修改方式及分组';
          }
          if (
            (type === GroupModifyType.Append || type === GroupModifyType.Overwrite) &&
            groups.length === 0
          ) {
            return '请选择商品分组修改方式及分组';
          }
          return void 0;
        }
      }
    ]
  },
  [ONLINE_GOODS_TEMPLATE]: {
    component: GoodsTemplateSelectField,
    props: {
      width: 195
    }
  },
  [ONLINE_GOODS_INDEX]: {
    component: GoodsIndexConditionCpn
  },
  [ONLINE_GOODS_NAME]: {
    component: RenameConditionCpn,
    validate: [
      {
        type: 'validator',
        rule: (value: any = {}) => {
          const { rule, prefix, suffix, replace } = value;
          if (isNil(rule)) {
            return '请选择名称修改方式及内容';
          }
          if (rule === Rules.Add && !prefix && !suffix) {
            return '前缀与后缀不能同时为空';
          }
          if (rule === Rules.Replace && !replace) {
            return '原有文字不能为空';
          }
          return void 0;
        }
      }
    ]
  },
  [ONLINE_MEMBER_DISCOUNT]: {
    component: DiscountSelectField,
    props: {
      width: 195
    }
  },
  [ONLINE_SALE_TIME]: {
    component: SaleTimeField,
    props: {
      selectWidth: 195
    }
  },
  [SCHEDULE_DISPLAY_OFF]: {
    component: ScheduleDisplayOffConditionCpn
  },
  [ONLINE_BUY_LIMIT]: {
    component: BuyLimitConditionCpn,
    props: {
      buyLimitTypes: isWscSingleStore
        ? undefined
        : compact([
            IsUnityModelWithoutFwh ? BuyLimitType.OrderQuota : null,
            BuyLimitType.Always,
            BuyLimitType.PeriodQuota
          ])
    }
  },
  [ONLINE_DELIVERY_TEMPLATE]: {
    component: DeliveryTemplateField,
    props: {
      width: 195
    }
  },
  [ONLINE_ORIGIN]: {
    component: OriginPriceField,
    props: {
      width: 195
    },
    validate: [
      {
        type: 'required',
        message: '请填写划线价'
      }
    ]
  },
  [ONLINE_CATEGORY]: {
    component: CategoryConditionCpn,
    validate: [
      {
        type: 'validator',
        rule: (value: any = {}) => {
          const { onlineCategoryOperateType, leafCategory } = value;
          if (isNil(onlineCategoryOperateType)) {
            return '请选择商品类目';
          }
          if (onlineCategoryOperateType === FetchCategoryMethod.Handle && isNil(leafCategory)) {
            return '请选择手动修改的商品类目';
          }
          return void 0;
        }
      }
    ]
  },
  [ONLINE_AFTER_SALE]: {
    component: AfterSaleConditionCpn,
    validate: [
      {
        type: 'validator',
        rule: (value: any = {}) => {
          const { afterSaleOperateType, afterSaleValue = [] } = value;
          if (isNil(afterSaleOperateType) || afterSaleValue.length === 0) {
            return '请选择售后服务修改方式及服务';
          }
          return void 0;
        }
      }
    ]
  },
  [ONLINE_GOODS_NO]: {
    component: GoodsNoConditionCpn,
    props: {
      width: 195,
      placeholder: '请输入商品编码'
    },
    validate: [
      {
        type: 'required',
        message: '请填写商品编码'
      }
    ]
  },
  [ONLINE_BARCODE]: {
    component: BarCodeConditionCpn,
    props: {
      width: 195,
      placeholder: '请输入商品条码'
    },
    validate: [
      {
        type: 'required',
        message: '请填写商品条码'
      }
    ]
  },
  [ONLINE_DISTRIBUTION]: {
    component: DistributionConditionCpn
  },
  [ONLINE_START_SALE_NUM]: {
    component: SaleNumStartConditionCpn
  },

  [ONLINE_SALE_TYPE]: {
    component: SaleTypeConditionCpn
  },
  [ONLINE_SHOW_STOCK]: {
    component: StockShowCondition
  },
  [ONLINE_PREPARE_TIME]: {
    component: PrepareTimeBatch,
    props: {
      shortLabel: false
    }
  },
  [ONLINE_CLASSIFICATION]: {
    component: GoodsClassificationColumnCpn,
    props: {
      columnType: 'conditions'
    }
  },
  [ONLINE_BRAND]: {
    component: GoodsBrandColumnCpn,
    props: {
      columnType: 'conditions'
    }
  },
  [ONLINE_HEAVY_CONTINUED]: {
    component: HeavyContinuedField,
    props: {
      label: '续重收费：'
    }
  },
  [ONLINE_SCHEDULE_DISPLAY]: {
    component: ScheduleDisplayField,
    props: {
      targetName: ONLINE_SCHEDULE_DISPLAY
    }
  },
  [ONLINE_CRUCIAL_LABEL]: {
    component: GoodsSpecialLabelField,
    props: {
      label: '商品关键标签：'
    },
    validate: [
      { type: 'required', message: '请选择商品关键标签' }
    ]
  },
  [ONLINE_GENERAL_LABEL]: {
    component: GoodsSpecialLabelField,
    props: {
      label: '商品普通标签：'
    },
    validate: [
      { type: 'required', message: '请选择商品普通标签' }
    ]
  },
  [ONLINE_MESSAGES]: {
    component: CommentColumn,
    props: {
      label: '商品留言：'
    },
    validate: [
      { type: 'required', message: '请选择商品留言' }
    ]
  },
  [MEITUAN_GOODS_GROUP]: {
    component: ExternalGroupField,
    props: {
      label: '商品分组：',
      channel: SalesChannelEnum.MtWmChannelId
    },
    validate: [
      { type: 'required', message: '请选择商品分组' }
    ]
  },
  [ELEME_GOODS_GROUP]: {
    component: ExternalGroupField,
    props: {
      label: '商品分组：',
      channel: SalesChannelEnum.ElemeChannelId
    },
    validate: [
      { type: 'required', message: '请选择商品分组' }
    ]
  },
};
