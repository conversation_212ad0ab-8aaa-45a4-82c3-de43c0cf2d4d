import { useRef, useCallback, useEffect } from 'react';
import { Notify } from 'zent';
import { request } from '@youzan/retail-utils';

interface IBatchImportData {
  failNum: number;
  failDetailsUrl: string;
  successList: any[];
  successNum: number;
  [key: string]: any;
}

interface ILoadDataConfig {
  loadDataUrl: string;
  loadDataParams?: any;
  batchKeysUrl?: string;
  batchDataUrl?: string;
  progressUrl?: string;
  interval?: number;
  onImportSuccess?: (successList?: any, successNum?: number) => void;
  onImportFail?: (error: any) => void;
  onSetLoading?: (loading: boolean) => void;
  onShowResult?: (failNum: number, failDetailsUrl: string, successNum?: number) => void;
  isFromStorage: boolean;
}

export const useLoadData = (config: ILoadDataConfig) => {
  const {
    loadDataUrl,
    loadDataParams = {},
    batchKeysUrl,
    batchDataUrl,
    progressUrl,
    interval = 3,
    onImportSuccess,
    onImportFail,
    onSetLoading,
    onShowResult,
    isFromStorage,
  } = config;

  const timerRef = useRef<NodeJS.Timeout>();

  const reset = useCallback(() => {
    clearTimeout(timerRef.current);
    onSetLoading?.(false);
  }, [onSetLoading]);

  const handleImportSuccess = useCallback((
    failNum: number,
    failDetailsUrl: string,
    successNum = 0,
    successList: any = [],
    totalNum = 0,
  ) => {
    const handledSuccessNum = successNum ? successNum : totalNum - failNum;
    onImportSuccess?.(successList, handledSuccessNum);
    onShowResult?.(failNum, failDetailsUrl, handledSuccessNum);
    reset();
  }, [onImportSuccess, onShowResult, reset]);

  const getImportData = useCallback((serialNo: string, dataNo: string) => {
    onSetLoading?.(true);

    return request({
      url: batchDataUrl!,
      method: 'POST',
      data: {
        serialNo,
        dataNo
      }
    })
      .then(res => {
        return res;
      })
      .catch(error => {
        Notify.error(error?.msg || '获取导入结果失败');
        reset();
        onImportFail?.(error);
      });
  }, [batchDataUrl, onSetLoading, reset, onImportFail]);

  const getProgress = useCallback((serialNo: number) => {
    onSetLoading?.(true);

    request({
      url: progressUrl!,
      method: 'POST',
      data: {
        serialNo
      }
    })
      .then(({ progress, successList, successNum, failNum, failDetailsUrl, totalNum }) => {
        if (+progress === 100) {
          handleImportSuccess(failNum, failDetailsUrl, successNum, successList, totalNum);
        } else {
          timerRef.current = setTimeout(() => getProgress(serialNo), interval * 1000);
        }
      })
      .catch(error => {
        Notify.error(error?.msg || '获取处理进度失败');
        reset();
        onImportFail?.(error);
      });
  }, [progressUrl, onSetLoading, handleImportSuccess, interval, reset, onImportFail]);

  const getDataKeys = useCallback((serialNo: string) => {
    onSetLoading?.(true);

    request({
      url: batchKeysUrl!,
      method: 'POST',
      data: {
        serialNo
      }
    })
      .then(({ progress, dataKeys = [], failNum, failDetailsUrl }) => {
        if (+progress === 100) {
          // 全部导入失败
          if (dataKeys?.length === 0) {
            handleImportSuccess(failNum, failDetailsUrl);
            return;
          }

          const list = dataKeys.reduce((prev: string[], curr: string) => {
            return [...prev, getImportData(serialNo, curr)];
          }, []);

          Promise.all(list).then(res => {
            const successList = res.reduce((prev: any[], curr: IBatchImportData) => {
              return [...prev, ...curr.successList];
            }, []);

            // 取最后一批次的返回值
            const { successNum, failNum, failDetailsUrl, totalNum }: Partial<IBatchImportData> = res[
              res.length - 1
            ];

            handleImportSuccess(failNum, failDetailsUrl, successNum, successList, totalNum);
          });
        } else {
          timerRef.current = setTimeout(() => getDataKeys(serialNo), interval * 1000);
        }
      })
      .catch(error => {
        Notify.error(error?.msg || '获取处理进度失败');
        reset();
        onImportFail?.(error);
      });
  }, [batchKeysUrl, onSetLoading, handleImportSuccess, getImportData, interval, reset, onImportFail]);

  const handleLoadData = useCallback(() => {
    request({
      url: loadDataUrl,
      method: 'GET',
      data: loadDataParams
    })
      .then(serialNo => {
        if (batchKeysUrl && batchDataUrl) {
          getDataKeys(serialNo);
          return;
        }

        getProgress(serialNo);
      })
      .catch(err => {
        Notify.error(err.msg || '提交任务失败');
        onSetLoading?.(false);
        onImportFail?.(err);
      });
  }, [
    loadDataUrl,
    loadDataParams,
    batchKeysUrl,
    batchDataUrl,
    getDataKeys,
    getProgress,
    onSetLoading,
    onImportFail
  ]);

  useEffect(() => {
    if (isFromStorage) {
      handleLoadData()
    }
  }, [])

  return {
    handleLoadData,
    reset
  };
};