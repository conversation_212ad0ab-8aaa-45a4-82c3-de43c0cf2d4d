/* eslint-disable no-nested-ternary */
import * as React from 'react';
import { C<PERSON>Lines, IGridColumn, Icon, Notify } from 'zent';
import { Pop } from '@zent/compat';
import { Link as SamLink, checkAccess } from '@youzan/sam-components';
import { BlankLink, Divider } from '@youzan/react-components';
import { PopInfo, LinkButton, createDialog } from '@youzan/retail-components';
import { formatDatetime, setUrlDomain, global } from '@youzan/retail-utils';
import {
  // isSingleStore,
  isFrontWarehouse,
  isRetailShop,
  isRetailSingleStore
} from '@youzan/utils-shop';
import formatMoney from '@youzan/utils/money/format';
import { GoodsType, SellChannel } from '@youzan/zan-hasaki';
import { EnumMarkCode } from '@youzan/goods-domain-definitions';
import { replaceCdnDomain } from 'common/utils';

import EditableCell from 'cpn/goods-item/editable-cell';
import CatRoute from 'cpn/cat-route';
import { createProductionTimeColumn } from 'cpn/columns';
import { BatchUpdateStock } from 'cpn/batch-update-stock';
import { VipPriceSingleButton, VipPriceSingleNewButton } from 'cpn/vip-price-button';
import { UseNewVipPriceBtn } from 'cpn/vip-price-button/helper';
import cx from 'classnames';

import {
  IsUnityModelWithoutFwh,
  HasSupplyChainAbility,
  HasCategoryAbility,
  HasHeadShareStockAbility,
  PromotionItemStatus,
  OfflineStoreGoodsQueryAbilityValidOrExpired,
  HasMultiChannelProductManageAbility,
  OfflineStatusDesc,
  MinPriceV2
} from 'common/constants';
import { QuickUpdateNum } from 'cpn/quick-update-num';

import { GoodsStatusDrawer } from 'cpn/goods-status-drawer';
import { SalesShopDrawer } from 'cpn/sales-shop-drawer';
import { renderChannelWithCounts } from './helper';
import { ProductSearchDTO, ProductIdDTO } from '.';
import css from './columns.scss';
import {
  deliveryTypeToLabel,
  channelOptions,
  channelGoodsId,
  ProdItemMigrationTag,
  REAL_GOODS,
  HOTEL_GOODS,
  PAID_LEVEL_GOODS,
  COUPON_GOODS
} from './constants';
import GoodsState from './components/goods-state';
import EditAction from './components/edit-action';
import { SellState } from './components/goods-state/table';
import { openSupplierInfoDialog } from './components/supplier-info';
import { MultiChannelDrawer } from './components/sales-channel-drawer';
import { GoodsAttrDetail } from './components/goods-attr/col-attr';
import GoodsPromotionInner from './promotion';

const {
  canSetCustomVipPrice: isVipPriceEnabled = false,
  showChannelTotalStock = false
}: {
  canSetCustomVipPrice?: boolean;
  showChannelTotalStock?: boolean;
} = global.BUSINESS;

interface IColumnProps {
  id2cat: {
    [key: number]: string;
  };
  onUpdateName: ({ spuId, name }: { spuId: number; name: string }) => void;
  onSuccess?: () => void;
  onRebuild?: (itemIds: number[]) => void;
  fetch: (queryInfo?: Record<string, any>) => void;
}

interface IChannelOption {
  key: SellChannel;
  text: string;
}

const { isInformPickUpFoodOn = false, inNewMemberPrice = false } = global.BUSINESS;

const getColumns = ({ id2cat, onUpdateName, onSuccess, fetch, onRebuild }: IColumnProps) => {
  const columns: (IGridColumn<ProductSearchDTO> & { hidden?: boolean })[] = [
    {
      title: '商品名称',
      fixed: 'left',
      width: 320, // 宽度不能小于这个，因为 EditableCell 编辑状态右侧会多出一个[保存]按钮
      bodyRender: ({
        title,
        imageUrl,
        barCode,
        isVirtual,
        idSummary: { spuId },
        goodsType,
        promotionItemStatus,
        prodItemMigrationTag
      }) => {
        // 虚拟商品，电子卡券
        const isForbiddenGoodsType = [2, 3].includes(isVirtual);
        const isFenxiao = GoodsType.FenXiao === goodsType;
        const isAiguang = promotionItemStatus === PromotionItemStatus.InPromotion;
        const isLegacy =
          prodItemMigrationTag === ProdItemMigrationTag.Pending ||
          prodItemMigrationTag === ProdItemMigrationTag.Done;
        return (
          <EditableCell
            canEdit={!isFrontWarehouse && !isForbiddenGoodsType && !isFenxiao && !isLegacy}
            disallowEditTips={
              isLegacy
                ? '历史商品请在 “编辑” 功能中修改名称'
                : isForbiddenGoodsType
                ? '虚拟商品/电子卡券请在 “编辑” 功能中修改名称'
                : ''
            }
            onUpdateName={(goodsName: string) => onUpdateName({ spuId, name: goodsName })}
            name={title}
            photoUrl={replaceCdnDomain(imageUrl)}
            spuNo={isForbiddenGoodsType ? '' : barCode}
            isFenxiao={isFenxiao}
            isAiguang={isAiguang}
            minPrice={MinPriceV2}
          />
        );
      }
    },
    {
      title: '商品分类',
      width: 88,
      bodyRender(data) {
        return (
          <Pop trigger="hover" position="top-left" content={<CatRoute id={data.categoryId} />}>
            <div>{id2cat[data.categoryId] || '-'}</div>
          </Pop>
        );
      }
    },
    {
      title: '品牌',
      width: 60,
      name: 'brandName',
      bodyRender: ({ brandName }) => <ClampLines lines={1} text={brandName || '-'} />
    },
    {
      title: '销售渠道',
      width: HasMultiChannelProductManageAbility ? 160 : 90,
      bodyRender: data => {
        // 组合商品（页面复用）、单店类型 不展示渠道数量
        // return isSingleStore ? renderChannelTextLabel(data) : renderChannelWithCounts(data);
        return HasMultiChannelProductManageAbility ? (
          <MultiChannelDrawer goodsInfo={data} />
        ) : (
          renderChannelWithCounts(data)
        );
      }
    },
    {
      title: (
        <PopInfo content="总库存" position="top-left" popContent="网店共享总部库存时需要使用库存" />
      ),
      width: 160,
      bodyRender: ({
        markCode,
        stockSummary,
        kdtId,
        itemId,
        goodsType,
        spotStockNum,
        planStockNum,
        totalStock,
        canEditPlanStock: isHqSupportPlanStock
      }) => {
        const stock = stockSummary?.headShareStock ?? 0;
        if (goodsType === GoodsType.FenXiao) {
          return '-';
        }
        const handleClick = () => {
          if (markCode === EnumMarkCode.Coupon) {
            Notify.warn('付费优惠券不支持列表页库存的快捷编辑');
            return;
          }
          createDialog({
            title: '改库存',
            style: { width: isHqSupportPlanStock ? 1000 : 800 },
            children: ({ onCancel }) => (
              <BatchUpdateStock
                itemIds={[itemId]}
                subKdtIds={[kdtId]}
                onCancel={onCancel}
                isAtLeastOneSupportPlanStock={isHqSupportPlanStock}
                showSkuDisableStatus={global.BUSINESS.showSkuDisableStatus}
                onConfirmSuccess={() => {
                  onCancel();
                  onSuccess();
                }}
              />
            )
          });
        };

        if (isHqSupportPlanStock) {
          return (
            <div>
              <p>现货：{spotStockNum || 0}</p>
              <p>计划：{planStockNum || 0}</p>
              <div className={css.total_stock}>
                总库存：{totalStock || 0}
                <Icon type="edit-o" onClick={handleClick} />
              </div>
            </div>
          );
        }

        return <LinkButton onClick={handleClick}>{stock}</LinkButton>;
      },
      hidden: !HasHeadShareStockAbility
    },
    {
      title: (
        <PopInfo content="门店总库存" position="top-left" popContent="所有门店渠道的库存之和" />
      ),
      width: 115,
      bodyRender: ({ stockSummary, goodsType }) => {
        if (goodsType === GoodsType.FenXiao) {
          return '-';
        }
        return stockSummary?.offlineStock ?? 0;
      },
      hidden: !OfflineStoreGoodsQueryAbilityValidOrExpired || !showChannelTotalStock
    },
    {
      title: (
        <PopInfo content="网店总库存" position="top-left" popContent="所有网店渠道的库存之和" />
      ),
      width: 115,
      bodyRender: ({ stockSummary }) => stockSummary?.onlineStock ?? 0,
      hidden: !showChannelTotalStock
    },
    {
      title: '销量',
      name: 'sumSoldNum',
      width: 60,
      bodyRender: ({ soldNumSummary }) => {
        return (
          <>
            <span className={css.total}>{soldNumSummary.sumSoldNum}</span>
            <Pop
              trigger="hover"
              position="top-center"
              content={
                <>
                  {OfflineStoreGoodsQueryAbilityValidOrExpired && (
                    <div>门店销量：{soldNumSummary.offlineSoldNum ?? '-'}</div>
                  )}
                  <div>网店销量：{soldNumSummary.onlineSoldNum ?? '-'}</div>
                </>
              }
            >
              <Icon type="info-circle" className={css.icon} />
            </Pop>
          </>
        );
      }
    },
    {
      title: '销售店铺',
      name: 'saleableShop',
      width: 100,
      bodyRender: (dto: ProductSearchDTO) => {
        const { saleableShop = { saleableShopCount: 0, noSaleShopCount: 0 } } = dto;
        return (
          <>
            {[
              ['可售', saleableShop.saleableShopCount, true] as const,
              ['不可售', saleableShop.noSaleShopCount, false] as const
            ].map(([label, value, saleable]) => (
              <div key={label}>
                {label}：
                <SalesShopDrawer
                  title={`${label}店铺`}
                  itemId={dto.itemId}
                  saleable={saleable}
                  value={value}
                />
              </div>
            ))}
          </>
        );
      }
    },
    {
      title: '商品状态',
      name: 'displayShop',
      width: 100,
      bodyRender: (dto: ProductSearchDTO) => {
        const { displayShop } = dto;
        return (
          <>
            {[
              ['销售中', displayShop.onSellShopCount, SellState.Selling] as const,
              ['已售罄', displayShop.sellOutShopCount, SellState.SoldOut] as const,
              [OfflineStatusDesc, displayShop.offlineShopCount, SellState.InCargo] as const
            ].map(([label, value, queryState]) => (
              <div key={label}>
                {label}：
                {HasMultiChannelProductManageAbility ? (
                  <GoodsStatusDrawer
                    title={`${label}店铺`}
                    itemId={dto.itemId}
                    channels={(dto?.sellChannelSettings ?? []).map(({ channel }) => channel)}
                    state={queryState}
                    value={value}
                  />
                ) : value > 0 ? (
                  <GoodsState title={`${label}店铺`} dto={dto} state={queryState}>
                    {value}
                  </GoodsState>
                ) : (
                  value
                )}
              </div>
            ))}
          </>
        );
      }
    },
    {
      title: '配送方式',
      name: 'distributionSearch',
      width: 90,
      bodyRender: ({ deliverySetting = {} }) => (
        <>
          {Object.keys(deliverySetting)
            .filter(type => deliverySetting[type as keyof typeof deliverySetting])
            .map(type => (
              <div key={type}>{deliveryTypeToLabel.get(type)}</div>
            ))}
        </>
      )
    },
    {
      title: '价格',
      name: 'productPrice',
      width: 90,
      bodyRender: ({ productPrice }: any) => {
        return productPrice ? formatMoney(productPrice, true, true) : '-';
      },
      hidden: !IsUnityModelWithoutFwh
    },
    {
      title: '库存单位',
      name: 'unit',
      width: 88,
      hidden: !HasSupplyChainAbility
    },
    {
      title: '商品类目',
      width: 88,
      name: 'leafCategoryName',
      hidden: !HasCategoryAbility,
      bodyRender: ({ leafCategoryName }) => <ClampLines lines={1} text={leafCategoryName || '-'} />
    },
    {
      title: (
        <PopInfo
          content="生命周期"
          position="top-left"
          popContent={
            <>
              <span>
                商品生命周期用于定位每个商品所处的阶段，
                <br />
                以及制定相应的销售策略{' '}
              </span>
              <BlankLink href={setUrlDomain('/setting/common/goods', 'store')}>配置规则</BlankLink>
            </>
          }
        />
      ),
      width: 105,
      bodyRender: ({ lifecycleName }) => lifecycleName || '-'
    },
    ...(isInformPickUpFoodOn ? [createProductionTimeColumn({ width: 88 }) as any] : []),
    ...(IsUnityModelWithoutFwh
      ? [
          {
            title: '商品属性',
            name: 'goodsAttr',
            width: 200,
            bodyRender: (data: ProductSearchDTO) => {
              return <GoodsAttrDetail data={data} />;
            }
          }
        ]
      : []),
    {
      title: '序号',
      name: 'num',
      width: 100,
      bodyRender: data => {
        const { num, itemId } = data;

        return <QuickUpdateNum value={num} itemId={itemId} onConfirmSuccess={fetch} />;
      }
    },
    {
      title: '创建时间',
      name: 'createdTime',
      needSort: true,
      width: 150,
      bodyRender: ({ createdTime }) => formatDatetime(createdTime)
    },
    {
      title: '操作',
      textAlign: 'right',
      fixed: 'right',
      width: 223,
      bodyRender(data) {
        const {
          itemId,
          /** 商品kdtId */
          kdtId,
          idSummary,
          idSummary: { onlineItemId, spuId, offlineItemId },
          markCode,
          memberCardAlias,
          alias,
          url,
          title,
          imageUrl,
          sellChannelSettings = [],
          goodsType,
          fxChangeStatus,
          prodItemMigrationTag,
          itemAdDetail,
          lockedByAd,
          goodsPlatform,
          itemLockTypes = [],
          lifecycleName,
          lifecycleId,
          hasPeriodStockNum
        } = data;
        const isLegacy =
          prodItemMigrationTag === ProdItemMigrationTag.Done ||
          prodItemMigrationTag === ProdItemMigrationTag.Pending;

        const isFenxiao = goodsType === GoodsType.FenXiao;

        const canCopy =
          goodsType === GoodsType.Actual && markCode !== EnumMarkCode.PaidLevel && !isLegacy;

        let editPath = `/v2/goods/library/#/edit/${onlineItemId}`;

        if (isFenxiao) {
          editPath = `/v4/goods/publish#/?id=${onlineItemId}`;
        }

        if (isLegacy) {
          editPath = `/v4/goods/publish#/?id=${onlineItemId}&legacy=1`;
        }

        //  是否有售卖的渠道
        const hasSellChannel = sellChannelSettings.length > 0;

        /** 是否有网店渠道 */
        const hasOnlineChannel = sellChannelSettings.some(
          item => item.channel === SellChannel.Online
        );

        // 是否是权益卡商品
        const isMemberCard = markCode === EnumMarkCode.MemberCard;

        function createEditAction() {
          // 商品库不支持权益卡商品编辑
          if (isMemberCard) {
            return (
              <Pop
                trigger="click"
                position="left-center"
                content="权益卡商品请到[客户-卡券管理]进行编辑"
                onConfirm={() => {
                  window.location.href = `/v4/scrm/membercard/edit/${memberCardAlias}`;
                }}
              >
                <SamLink name="编辑" disabled={hasPeriodStockNum} />
              </Pop>
            );
          }

          return (
            <EditAction
              editDisabled={hasPeriodStockNum}
              editPath={editPath}
              lifecycleName={lifecycleName}
              lifecycleId={lifecycleId}
            />
          );
        }

        const editAction = createEditAction();

        const detailAction = (
          <SamLink
            key="detail"
            name="详情"
            disabled={hasPeriodStockNum}
            href={`#/detail/${spuId}`}
            blank
          />
        );

        const actions = [editAction];

        if (isLegacy) {
          actions.unshift(
            prodItemMigrationTag === ProdItemMigrationTag.Done ? (
              <Pop trigger="hover" position="left-center" content="商品已重建，不支持再次重建。">
                <SamLink disabled name="重建" />
              </Pop>
            ) : (
              <SamLink onClick={() => onRebuild([onlineItemId])} name="重建" />
            )
          );
        }

        /* NOTE: 抽屉的展示依赖按钮的渲染，所以会员价需要放在前3的位置 */
        if (isVipPriceEnabled) {
          // 新版会员价并且有售卖渠道的
          let channels = [];
          const goodsIds: {
            [key: number]: number;
          } = {};
          // 售卖渠道转化select options 数据
          channels = sellChannelSettings.reduce((channelList, sellChannel) => {
            let list: IChannelOption[] = channelList;
            /** 只统计门店和网店 */
            if ([SellChannel.Online, SellChannel.Offline].includes(sellChannel.channel)) {
              list = [
                ...channelList,
                {
                  key: sellChannel.channel,
                  text: channelOptions[sellChannel.channel]
                }
              ];
            }
            return list;
          }, []);
          if (inNewMemberPrice && hasSellChannel) {
            sellChannelSettings.forEach(sellChannel => {
              const channelGoodsKey: keyof ProductIdDTO = channelGoodsId[sellChannel.channel];
              goodsIds[sellChannel.channel] = idSummary[channelGoodsKey];
            });
          }
          const newGoodsIds = [];
          if (
            onlineItemId &&
            sellChannelSettings.find(item => item.channel === SellChannel.Online)
          ) {
            newGoodsIds.push({
              itemId: onlineItemId,
              kdtId: data.kdtId,
              channel: 0
            });
          }
          if (
            offlineItemId &&
            sellChannelSettings.find(item => item.channel === SellChannel.Offline)
          ) {
            newGoodsIds.push({
              itemId: offlineItemId,
              kdtId: data.kdtId,
              channel: 1
            });
          }
          const vipPriceSingleAction = UseNewVipPriceBtn ? (
            <VipPriceSingleNewButton
              data={{
                goodsDetail: data,
                goodsIds: newGoodsIds
              }}
              onFetchData={fetch}
              disabled={!checkAccess('会员价') || hasPeriodStockNum}
            />
          ) : (
            <VipPriceSingleButton
              data={{
                goodsDetail: data,
                stateData: {
                  shopSelector: kdtId
                }
              }}
              childrenProps={{
                goodsId: goodsIds || {},
                channels
              }}
              onFetchData={fetch}
              showChannel
              disabled={!checkAccess('会员价') || hasPeriodStockNum}
            />
          );
          actions.push(vipPriceSingleAction);
        }

        if (isFenxiao) {
          actions.push(
            <SamLink
              disabled={hasPeriodStockNum}
              onClick={() => {
                openSupplierInfoDialog({
                  alias,
                  kdtId,
                  url,
                  title,
                  imageUrl
                });
              }}
              name="供货详情"
            />
          );
        }

        const { KDT_ID: DEFAULT_SHOP_SELECTED_ID } = global;
        const {
          shopInfo = {},
          userInfo = {},
          weappConfig = {},
          dspConfigs = {},
          hasOrderWeapp = {},
          mpVersion = {},
          showBaiduWeappCode,
          hasBindWxWeapp,
          shopBindTxConfig,
          goods_share_page_style
        } = global.BUSINESS;
        const { roles = [] } = userInfo;
        const { readStatus } = itemAdDetail || {};
        const showAdReadTips = readStatus === false;
        const isPaidLevelGoods = markCode === PAID_LEVEL_GOODS.markCode;
        const isCouponGoods = markCode === COUPON_GOODS.markCode;
        const isSupportAdsGoods =
          [REAL_GOODS.value, HOTEL_GOODS.value].indexOf(goodsType) !== -1 &&
          +goodsPlatform !== 10 &&
          itemLockTypes.length === 0 &&
          !isPaidLevelGoods;
        /** 判断店铺是否已绑定腾讯广点通 */
        const IS_BIND_TX_SHOP = shopBindTxConfig === 1;
        const showTxAdsBtn = lockedByAd || (IS_BIND_TX_SHOP && isSupportAdsGoods);
        // 隐藏创建二维码：零售-非单店或者选了某个分店 或付费等级商品、优惠券商品
        const hideCreateQrcode =
          (isRetailShop ? !isRetailSingleStore || DEFAULT_SHOP_SELECTED_ID !== kdtId : false) ||
          isPaidLevelGoods ||
          isCouponGoods;

        /** 推广弹窗需要的 _global 下的变量 */
        const promoteNeedGlobal = {
          shopInfo,
          isUnityModel: IsUnityModelWithoutFwh,
          userRoleList: roles,
          isSuperStore: shopInfo.shopType === 7,
          weappConfig,
          dspConfigs,
          hasOrderWeapp,
          hasOnlineWeapp: !!mpVersion.releaseVersion,
          showBaiduWeappCode,
          hasBindWxWeapp,
          isShareStyle: Boolean(goods_share_page_style)
        };

        // 付费等级不展示推广 || 没有发布到网店渠道时不能展示推广
        if (markCode !== EnumMarkCode.PaidLevel && hasOnlineChannel) {
          actions.push(
            <GoodsPromotionInner
              data={data}
              showAdReadTips={showAdReadTips}
              showTxAdsBtn={showTxAdsBtn}
              hideCreateQrcode={hideCreateQrcode}
              fetch={fetch}
              fxChangeStatus={fxChangeStatus}
              hasPeriodStockNum={hasPeriodStockNum}
              promoteNeedGlobal={promoteNeedGlobal}
            />
          );
        }

        // 仅支持复制可创建的商品类型
        if (canCopy) {
          actions.push(
            <SamLink
              blank
              key="copy"
              name="复制"
              disabled={hasPeriodStockNum}
              href={`/v2/goods/library#/copy/${onlineItemId}`}
            />
          );
        } else {
          actions.push(<SamLink disabled name="复制" />);
        }

        // 如果是付费等级商品，不展示查看数据
        if (markCode !== EnumMarkCode.PaidLevel) {
          actions.push(
            /**
             * @see wsc-pc-goods/client/pages/goods-manage/components/table/columns/operations/statcenter/adaptor.tsx
             */
            <SamLink
              name="查看数据"
              blank
              href={`/v2/data/goods?channelType=chain_all&goodsId=${itemId}&keyword=${title}`}
            />
          );
        }

        return (
          <>
            <Divider
              mode="ellipsis"
              items={isFrontWarehouse ? [detailAction] : actions.filter(Boolean)}
            />
            {isLegacy ? (
              prodItemMigrationTag === ProdItemMigrationTag.Done ? (
                <p className={cx(css.abnormal_tip, 'built')}>
                  <Icon type="error-circle" />
                  历史商品已重建
                </p>
              ) : (
                <Pop
                  trigger="hover"
                  position="left-center"
                  content={
                    <>
                      历史商品是指未成功升级的商品，不能使用外卖渠道管理功能，建议将其重建。{' '}
                      <SamLink name="重建" onClick={() => onRebuild([onlineItemId])}>
                        去重建
                      </SamLink>
                    </>
                  }
                >
                  <p className={css.abnormal_tip}>
                    <Icon type="error-circle" />
                    历史商品
                  </p>
                </Pop>
              )
            ) : null}
          </>
        );
      }
    }
  ];

  return columns.filter(item => !item.hidden);
};

export default getColumns;
