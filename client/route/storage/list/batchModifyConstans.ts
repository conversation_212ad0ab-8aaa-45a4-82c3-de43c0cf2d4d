import { ELEME_GOODS_GROUP, MEITUAN_GOODS_GROUP, MEIT<PERSON>AN_SHANGOU_GOODS_GROUP, OFFLINE_BUY_LIMIT, OFFLINE_GOODS_GROUP, OFFLINE_GOODS_NAME, OFFLINE_GENERAL_LABEL, OFFLINE_CRUCIAL_LABEL, OFFLINE_MEMBER_DISCOUNT, ONLINE_BUY_LIMIT, ONLINE_GOODS_GROUP, ONLINE_GOODS_NAME, ONLINE_GENERAL_LABEL, ONLINE_CRUCIAL_LABEL, ONLINE_MEMBER_DISCOUNT } from "route/batch-operate/modify/fields";
import { GoodsBatchUpdate<PERSON>han } from "./constants";
import { ProductType } from "route/batch-operate/constant";

export const GROUP_BATCH_MODIFY_DATA = [
  {
    fields: [ONLINE_GOODS_GROUP],
    key: GoodsBatchUp<PERSON><PERSON><PERSON>.Online,
    channelFields: {
      [ProductType.Online]: [ONLINE_GOODS_GROUP]
    },
    channel: ProductType.Online,
  },
  {
    fields: [OFFLINE_GOODS_GROUP],
    key: GoodsBatchUpdate<PERSON>han.Offline,
    channelFields: {
      [ProductType.Offline]: [OFFLINE_GOODS_GROUP]
    },
    channel: ProductType.Offline,
  },
  {
    fields: [MEITUAN_SHANGOU_GOODS_GROUP],
    key: GoodsBatchUpdateChan.ShanGou,
    channelFields: {
      [ProductType.MeituanShangou]: [MEITUAN_SHANGOU_GOODS_GROUP]
    },
    channel: ProductType.MeituanShangou,
  },
  {
    fields: [MEITUAN_GOODS_GROUP],
    key: GoodsBatchUpdateChan.MeiTuan,
    channelFields: {
      [ProductType.Meituan]: [MEITUAN_GOODS_GROUP]
    },
    channel: ProductType.Meituan,
  },
  {
    fields: [ELEME_GOODS_GROUP],
    key: GoodsBatchUpdateChan.Eleme,
    channelFields: {
      [ProductType.Eleme]: [ELEME_GOODS_GROUP]
    },
    channel: ProductType.Eleme,
  },
]

export const NAME_BATCH_MODIFY_DATA = [
  {
    fields: [ONLINE_GOODS_NAME],
    key: GoodsBatchUpdateChan.Online,
    channelFields: {
      [ProductType.Online]: [ONLINE_GOODS_NAME]
    },
    channel: ProductType.Online,
  },
  {
    fields: [OFFLINE_GOODS_NAME],
    key: GoodsBatchUpdateChan.Offline,
    channelFields: {
      [ProductType.Offline]: [OFFLINE_GOODS_NAME]
    },
    channel: ProductType.Offline,
  },
  
]

export const MEMBER_DISCOUNT_BATCH_MODIFY_DATA = [
  {
    fields: [ONLINE_MEMBER_DISCOUNT],
    key: GoodsBatchUpdateChan.Online,
    channelFields: {
      [ProductType.Online]: [ONLINE_MEMBER_DISCOUNT]
    },
    channel: ProductType.Online,
  },
  {
    fields: [OFFLINE_MEMBER_DISCOUNT],
    key: GoodsBatchUpdateChan.Offline,
    channelFields: {
      [ProductType.Offline]: [OFFLINE_MEMBER_DISCOUNT]
    },
    channel: ProductType.Offline,
  },
]

export const BUY_LIMIT_BATCH_MODIFY_DATA = [
  {
    fields: [ONLINE_BUY_LIMIT],
    key: GoodsBatchUpdateChan.Online,
    channelFields: {
      [ProductType.Online]: [ONLINE_BUY_LIMIT]
    },
    channel: ProductType.Online,
  },
  {
    fields: [OFFLINE_BUY_LIMIT],
    key: GoodsBatchUpdateChan.Offline,
    channelFields: {
      [ProductType.Offline]: [OFFLINE_BUY_LIMIT]
    },
    channel: ProductType.Offline,
  },
]

export const NORMAL_LABEL_BATCH_MODIFY_DATA = [
  {
    fields: [ONLINE_GENERAL_LABEL],
    key: GoodsBatchUpdateChan.Online,
    channelFields: {
      [ProductType.Online]: [ONLINE_GENERAL_LABEL]
    },
    channel: ProductType.Online,
  },
  {
    fields: [OFFLINE_GENERAL_LABEL],
    key: GoodsBatchUpdateChan.Offline,
    channelFields: {
      [ProductType.Offline]: [OFFLINE_GENERAL_LABEL]
    },
    channel: ProductType.Offline,
  },
]

export const SPECIAL_LABEL_BATCH_MODIFY_DATA = [
  {
    fields: [ONLINE_CRUCIAL_LABEL],
    key: GoodsBatchUpdateChan.Online,
    channelFields: {
      [ProductType.Online]: [ONLINE_CRUCIAL_LABEL]
    },
    channel: ProductType.Online,
  },
  {
    fields: [OFFLINE_CRUCIAL_LABEL],
    key: GoodsBatchUpdateChan.Offline,
    channelFields: {
      [ProductType.Offline]: [OFFLINE_CRUCIAL_LABEL]
    },
    channel: ProductType.Offline,
  },
]
