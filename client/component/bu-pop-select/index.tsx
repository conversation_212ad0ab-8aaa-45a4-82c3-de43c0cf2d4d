import React, { useState, isValidElement } from 'react';
import { noop } from 'lodash';
import cx from 'classnames';

import { IsRetailMinimalistOrEduV4 } from 'common/constants';
import { ShopSelectDialog } from '@youzan/react-components';
import { Link as SamLink } from '@youzan/sam-components';
import { IShopItem } from '@youzan/react-components/typings/components/shop-select/types';
import { IGridSelectionProps } from 'common/types';
import { SelectBuDialog } from './pop-selector';
import { BU_TYPE } from './const';

import style from './index.scss';

export interface IProps {
  biz: string;
  value: number[];
  onConfirm: (val: number[]) => void;
  className?: string;
  showShopTag?: boolean;
  renderCustomTrigger?: (length: number, openDialog: () => void) => any;
  extraParams?: any;
  getSelectionProps?: (data: IShopItem) => Partial<IGridSelectionProps>;
  shopModel?: any;
  columns?: any[];
}

const BuPopSelect: React.FC<IProps> = props => {
  const {
    value = [],
    onConfirm = noop,
    className,
    biz,
    showShopTag,
    renderCustomTrigger,
    extraParams,
    getSelectionProps,
    shopModel,
    columns
  } = props;

  console.log('biz', biz);
  
  const { length } = value;

  const [visible, setVisible] = useState(false);

  const openDialog = () => {
    setVisible(true);
  };

  const closeDialog = () => {
    setVisible(false);
  };

  const handleConfirm = (kdtIds: number[]) => {
    onConfirm(kdtIds);
    closeDialog();
  };

  const renderTrigger = () => {
    const DefaultTrigger = (
      <>
        已选店铺：{length}
        <SamLink className="open-select__dialog" onClick={openDialog}>
          选择店铺
        </SamLink>
      </>
    );
    if (!renderCustomTrigger || typeof renderCustomTrigger !== 'function') {
      return DefaultTrigger;
    }
    const CustomTrigger = renderCustomTrigger(length, openDialog);
    return isValidElement(CustomTrigger) ? CustomTrigger : DefaultTrigger;
  };

  return (
    <div className={cx(style.buSelectContainer, className)}>
      {renderTrigger()}
      {biz === BU_TYPE.online ? (
        <ShopSelectDialog
          defaultKdtIds={(value as any) as string[]}
          title="选择店铺"
          visible={visible}
          isShowShopGroup={IsRetailMinimalistOrEduV4}
          onClose={closeDialog}
          onConfirm={handleConfirm as any}
          extraParams={extraParams}
          shopModel={shopModel}
          columns={columns}
          getSelectionProps={getSelectionProps}
        />
      ) : (
        <SelectBuDialog
          visible={visible}
          biz={biz}
          showShopTag={showShopTag}
          value={value}
          onConfirm={onConfirm}
          closeDialog={closeDialog}
        />
      )}
    </div>
  );
};

export { BU_TYPE } from './const';

export default BuPopSelect;
