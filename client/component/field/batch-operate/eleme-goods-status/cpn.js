import React from 'react';
import { isNil } from 'lodash';
import { Select } from '@youzan/biz-select-center';
import { SalesChannelEnum } from '@youzan/goods-domain-pc-components';

import BuPopSelector, { BU_TYPE } from 'cpn/bu-pop-select';
import { MultiChannelShopColumns } from 'cpn/bu-pop-select/multi-channel';
import {
  HasMultiChannelProductManageAbility,
  IsMU,
  IsRetailMinimalistOrEduV4Hq,
  IsUnityModelUnifiedShop
} from 'common/constants';

import style from './index.scss';

export const NO_SELL = 8;
export const ON_SHELF = 1;
export const OFF_SHELF = 0;

const StatusTipMap = new Map([
  [ON_SHELF, '可售库存为 0 的商品上架后状态为"已售罄"，待添加库存后才可正常销售。'],
  [OFF_SHELF, '商品下架后状态为"仓库中"，不再支持销售']
]);

const STATUS_DATA = [
  {
    text: '上架',
    value: ON_SHELF
  },
  {
    text: '下架',
    value: OFF_SHELF
  },
  {
    text: '不可售',
    value: NO_SELL,
    hide: !(IsRetailMinimalistOrEduV4Hq || IsUnityModelUnifiedShop)
  }
].filter(({ hide }) => hide !== true);

const CommonParams = HasMultiChannelProductManageAbility
  ? {
      extraParams: {
        channelIds: [SalesChannelEnum.ElemeChannelId]
      },
      shopModel: 'allShop',
      columns: MultiChannelShopColumns
    }
  : {};

const ElemeGoodsStatus = ({ input, showTip = true }) => {
  const { value = {}, onChange } = input;
  const { subKdtIds = [], display } = value;

  const handleChangeShelf = val => {
    onChange({
      ...value,
      display: val
    });
  };

  const onChangeStore = val => {
    onChange({
      ...value,
      subKdtIds: [...val]
    });
  };

  return (
    <div className={style.index}>
      <div className="cpn-controls">
        <Select width={90} data={STATUS_DATA} value={value.display} onChange={handleChangeShelf} />
        {IsMU && (
          <BuPopSelector
            {...CommonParams}
            biz={BU_TYPE.online}
            value={subKdtIds}
            onConfirm={onChangeStore}
          />
        )}
      </div>
      {showTip && !isNil(display) && (
        <p className="zent-form__help-desc">{StatusTipMap.get(display)}</p>
      )}
    </div>
  );
};

export default ElemeGoodsStatus;
