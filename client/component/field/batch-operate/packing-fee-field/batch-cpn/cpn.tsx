import React from 'react';
import { NumberInput } from 'zent';
import './index.scss';
interface IProps {
  input: {
    value: any;
    onChange: (val: any) => void;
  };
}

const Component = (props: IProps) => {
  const { onChange, value = '' } = props.input;

  const handlePriceChange = (val: string) => {
    onChange(val);
  };

  return (
    <div className='packing-fee-container'>
      <NumberInput
        addonBefore="¥"
        inline
        width={140}
        decimal={2}
        value={value}
        onChange={handlePriceChange}
        min={0}
        max={2}
        placeholder="请输入"
      />
    </div>
  );
};

export default Component;
