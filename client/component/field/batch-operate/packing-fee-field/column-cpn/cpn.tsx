import React from 'react';
import { NumberInput } from 'zent';

interface IProps {
  input: {
    value: any;
    onChange: (val: any) => void;
  };
  goodsData?: any; // 商品数据，用于判断是否多sku
}

const Component = (props: IProps) => {
  const { onChange, value = '' } = props.input;

  const handlePriceChange = (val: string) => {
    onChange(val);
  };

  return (
    <div>
      <NumberInput
        addonBefore="¥"
        inline
        width={150}
        decimal={2}
        value={value}
        onChange={handlePriceChange}
        min={0}
        max={2}
        placeholder="请输入"
      />
    </div>
  );
};

export default Component;
