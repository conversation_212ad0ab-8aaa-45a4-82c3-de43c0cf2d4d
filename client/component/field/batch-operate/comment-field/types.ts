import { IFormControlProps } from 'zent';

/** 留言类型枚举 */
export enum CommentType {
  /** 文本格式  */
  Text = 'text',
  /** 数字格式  */
  Number = 'tel',
  /** 邮箱格式  */
  Email = 'email',
  /** 日期格式  */
  Date = 'date',
  /** 身份证格式  */
  IdCard = 'id_no',
  /** 图片格式  */
  Image = 'image',
  /** 手机号格式  */
  Mobile = 'mobile',
  /** 时间格式  */
  Time = 'time'
}

/** 留言数据接口 */
export interface CommentData {
  /** 留言标题 */
  title: string;
  /** 留言类型 */
  type: CommentType;
  /** 是否多行（仅文本类型有效） */
  multiple: boolean;
  /** 是否必填 */
  required: boolean;
  /** 是否包含日期（仅时间类型有效） */
  includeDate: boolean;
}

/** 留言组件属性接口 */
export interface ICommentProps extends IFormControlProps {
  /** 字段名称 */
  name?: string;
  /** 标签文本 */
  label?: string;
  /** 是否必填 */
  required?: boolean;
  /** 最大标题长度 */
  max?: number;
  /** 帮助描述 */
  helpDesc?: string;
  /** 是否禁用 */
  disabled?: boolean;
}
