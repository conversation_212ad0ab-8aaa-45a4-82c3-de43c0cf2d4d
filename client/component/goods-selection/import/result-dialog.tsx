import React from 'react';
import { Dialog, Button } from 'zent';

interface IProps {
  successNum: number;
  failNum: number;
  failDetailsUrl: string;
}

const { openDialog, closeDialog } = Dialog;
const dialogId = 'detail-dialog';

export function openResultDialog({ successNum, failNum, failDetailsUrl }: IProps) {
  const close = () => closeDialog(dialogId);
  const footer =
    failNum > 0 ? (
      <Button onClick={close} type="primary" target="_blank" href={failDetailsUrl}>
        下载未导入数据
      </Button>
    ) : (
      <Button onClick={close} type="primary">
        确认
      </Button>
    ); 

  openDialog({
    dialogId,
    title: '导入成功',
    children: (
      <div>
        商品导入成功，其中成功导入 {successNum} 条数据，未导入 {failNum} 条数据。
      </div>
    ),
    footer
  });
}
